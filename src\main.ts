import { enableProdMode } from '@angular/core';
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';

import { AppModule } from './app/app.module';
import { environment } from './environments/environment';

import { hmrBootstrap } from './hmr';

if (environment.production) {
  enableProdMode();
}

// Redirect from devui domain to cls-dev domain
const currentHost = window.location.hostname;
if (currentHost === "devui-cls.cmcati.vn") {
  const currentPath = window.location.pathname;
  const currentSearch = window.location.search;
  const currentHash = window.location.hash;
  
  // Remove /cls prefix if it exists to avoid duplication
  const cleanPath = currentPath.startsWith('/cls/') 
    ? currentPath.substring(4)  // Remove '/cls'
    : currentPath.startsWith('/cls')
    ? currentPath.substring(4)
    : currentPath;
  
  const target = `https://cls-dev.cmcai.vn${cleanPath}${currentSearch}${currentHash}`;
  window.location.replace(target);
}

const bootstrap = () => platformBrowserDynamic().bootstrapModule(AppModule);

if (environment.hmr) {
  if (module['hot']) {
    hmrBootstrap(module, bootstrap);
  } else {
    console.error('HMR is not enabled for webpack-dev-server!');
    // console.log('Are you using the --hmr flag for ng serve?');
  }
} else {
  bootstrap().catch(err =>  console.log(err));
}
