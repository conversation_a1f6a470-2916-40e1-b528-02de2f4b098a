.container-chatbot {
  position: relative;
}
.chat-content {
  padding: 12px;
  flex: 1;
  overflow-y: auto;
  height: 86%;
}

.message-bubble {
  margin-bottom: 10px;
  border-radius: 16px;
  line-height: 1.4;
}

.answer.bot {
  color: #000;
  align-self: flex-start;
}

.answer.user {
  background-color: rgba(245, 245, 245, 1);
  padding: 12px 16px;
  border-radius: 24px;
  color: #333;
  width: fit-content;
  margin-left: auto;
}

.chat-input {
  padding: 10px;
  border-top: 1px solid #eee;
  background-color: #fff;
}

.chat-input input {
  flex: 1;
  padding: 10px 12px;
  border-radius: 8px;
  border: 1px solid #ccc;
  outline: none;
  margin-right: 8px;
}

.btn-send {
  padding: 8px 16px;
  border: none;
  background-color: #007bff;
  color: white;
  border-radius: 8px;
  cursor: pointer;
}

.btn-send:hover {
  background-color: #0056b3;
}
::ng-deep.tab-content > .active {
  height: 100% !important;
}
.button-add-conversation {
  border: 1px solid rgba(224, 224, 224, 1);
  border-radius: 4px;
  padding: 6px;
  cursor: pointer;
}
.gradient-text {
  background: linear-gradient(
    to left,
    rgba(0, 97, 255) 0%,
    rgba(0, 97, 255, 0.8) 45%,
    rgb(0, 97, 255, 0) 50%,
    rgba(0, 97, 255, 0.8) 55%,
    rgba(0, 97, 255) 100%
  );
  background-size: 200% auto;
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  -webkit-text-fill-color: transparent;
  text-fill-color: transparent;
  animation: textShine 3s linear infinite;
}
.thinking {
}
@keyframes textShine {
  0% {
    background-position: -100% center;
  }
  100% {
    background-position: 100% center;
  }
}
::ng-deep #chatbot .accordion .card .card-header button {
  padding: 0 !important;
}
.scroll-to-bottom {
  bottom: 10%;
  background-color: #fff;
  right: 50%;
  position: absolute;
  z-index: 10;
  display: flex;
  box-shadow: -1px 4px 16px 0px rgba(0, 0, 0, 0.15);
  border-radius: 50%;
  padding: 5px;
  cursor: pointer;
}
.contain-input-chatbot {
  position: absolute;
  bottom: 0;
  width: 100%;
}
[contenteditable="true"] {
  border: 1px dashed #ccc !important;
  padding: 4px !important;
  min-height: 24px !important;
  display: inline-block;
}
.disabled-button {
  background-color: rgba(158, 158, 158, 1) !important; /* màu xám */
  cursor: not-allowed !important;
}
.chat-input-wrapper {
  display: flex;
  // align-items: center;
  // padding-top: 9px;
  padding: 8px 12px;
  border: 1px solid rgba(224, 224, 224, 1);
  border-radius: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  background-color: #fff;
  max-height: 200px;
  overflow-y: auto;
  overflow-x: hidden;
}

.chat-input {
  border: none;
  outline: none;
  resize: none;
  flex: 1;
  line-height: 1.5;
  padding: 6px 10px;
  background-color: transparent;
  padding-top: 9px;
}

.chat-button-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.chat-send-button,
.chat-cancel-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  background-color: #4285f4;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.chat-cancel-button {
  background-color: #f44336; /* red */
}

.chat-send-button svg,
.chat-cancel-button svg {
  width: 20px;
  height: 20px;
}
:host ::ng-deep ol {
  padding-left: 12px !important;
}
.coversation-contain {
  padding: 8px;
}

.compare-clause-chatbot-bold-black {
  color: #000 !important;
}

@media (max-width: 1199.98px) {
  .font-custom {
    font-size: 13px !important; /* font nhỏ hơn */
  }
}

.maximized {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  min-height: 0;
  background-color: #fff;
}

.custom-maximize {
  padding: 2rem 5rem;
}

.compare-clause-chatbot-content {
  max-width: 50vw;
  
}