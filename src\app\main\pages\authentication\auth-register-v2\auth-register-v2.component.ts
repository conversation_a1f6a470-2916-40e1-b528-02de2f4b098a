import { Component, Input, Output, OnInit, EventEmitter, ViewEncapsulation } from "@angular/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";

import { takeUntil } from "rxjs/operators";
import { Subject } from "rxjs";

import { CoreConfigService } from "@core/services/config.service";
import { AuthRegisterV2Service } from "./auth-register-v2.service";
import { ToastrService } from "ngx-toastr";
import { Router } from "@angular/router";
import { emailValidator } from "app/shared/EmailValidator";
import Swal from "sweetalert2";
import { SocialUser } from "@abacritt/angularx-social-login";

@Component({
  selector: "app-auth-register-v2",
  templateUrl: "./auth-register-v2.component.html",
  styleUrls: ["./auth-register-v2.component.scss"],
  encapsulation: ViewEncapsulation.None,
})
export class AuthRegisterV2Component implements OnInit {
  // Input property to receive socialUser from another component
  @Input() public socialUser: SocialUser | null = null;
  @Output() register: EventEmitter<any> = new EventEmitter<any>();

  // Public
  public coreConfig: any;
  public passwordTextType: boolean;
  public registerForm: FormGroup;
  public submitted = false;
  public isLoading = false;
  public is_email_valid: boolean = false;

  public wizardStep: number = 1;

  // Private
  private _unsubscribeAll: Subject<any>;

  /**
   * Constructor
   *
   * @param {CoreConfigService} _coreConfigService
   * @param {FormBuilder} _formBuilder
   * @param {AuthRegisterV2Service} _registerService
   */
  constructor(
    private _coreConfigService: CoreConfigService,
    private _formBuilder: FormBuilder,
    private _registerService: AuthRegisterV2Service,
    private _router: Router,
    private toastr: ToastrService,
  ) {
    this._unsubscribeAll = new Subject();

    // Configure the layout
    this._coreConfigService.config = {
      layout: {
        navbar: {
          hidden: true,
        },
        menu: {
          hidden: true,
        },
        footer: {
          hidden: true,
        },
        customizer: false,
        enableLocalStorage: false,
      },
    };
  }

  // convenience getter for easy access to form fields
  get f() {
    return this.registerForm.controls;
  }

  /**
   * Hàm kiểm tra email đã tồn tại hay chưa
   */
  checkEmail() {
    const email = this.f.email.value;
    this._registerService.checkEmail(email).subscribe(
      (res) => {
        this.is_email_valid = !res.exists;
        if (!this.is_email_valid) {
          this.f.email.setErrors({ ...(this.f.email.errors || {}), emailExists: true });
        } else {
          if (this.f.email.errors && this.f.email.errors.emailExists) {
            const { emailExists, ...rest } = this.f.email.errors;
            this.f.email.setErrors(Object.keys(rest).length ? rest : null);
          }
        }
      },
      (error) => {
        this.toastr.error('Đã có lỗi xảy ra khi kiểm tra email. Vui lòng thử lại sau.', 'Lỗi');
      }
    );
  }

  /**
   * Toggle password
   */
  togglePasswordTextType() {
    this.passwordTextType = !this.passwordTextType;
  }

  /**
   * On Submit
   */
  onSubmit() {
    this.submitted = true;
    // stop here if form is invalid
    if (this.registerForm.invalid) {
      return;
    }
    this.confirmOpen();
  }

  confirmOpen() {
    const data = {
      fullname: this.f.username.value,
      // password: this.f.password.value,
      email: this.f.email.value,
      // agree: this.f.agree.value,
      user_type: this.f.user_type.value,
      organization_name: this.f.organization_name.value,
      industry: this.f.industry.value,
      scale: this.f.scale.value,
      source: this.f.source.value,
      product_familiarity: this.f.product_familiarity.value,
      description: this.f.description.value
    };

    this.isLoading = true;
    this._registerService.registerReq(data).subscribe(
      (res) => {
        this.isLoading = false;
        let timerInterval: any;
        let timeLeft = 10;
        Swal.fire({
          icon: 'success',
          title: 'Đăng ký thành công!',
          html: `
            <div>
              Đăng ký tài khoản thành công. Kết quả phê duyệt sẽ được gửi qua Email, vui lòng kiểm tra email (Bao gồm thư rác/spam, quảng cáo) để cập nhật thông tin mới nhất
            </div>
          `,
          timer: 10000,
          timerProgressBar: true,
          showCloseButton: true,
          showConfirmButton: true,
          confirmButtonText: `<span id="swal-close-text">Đóng (${timeLeft})</span>`,
          onOpen: () => {
            timerInterval = setInterval(() => {
              if (timeLeft > 0) timeLeft--;
              const btn = document.getElementById('swal-close-text');
              if (btn) btn.textContent = `Đóng (${timeLeft})`;
            }, 1000);
          },
          onDestroy: () => {
            if (timerInterval) clearInterval(timerInterval);
          }
        }).then(() => {
          this.register.emit(res);
        });
      },
      (error) => {
        this.isLoading = false;
        let timerInterval: any;
        let timeLeft = 10;
        Swal.fire({
          icon: 'error',
          title: 'Đăng ký thất bại',
          html: `
            <div>
              ${error?.error?.message || 'Đã có lỗi xảy ra. Vui lòng thử lại sau!'}
            </div>
          `,
          confirmButtonText: `<span id="swal-close-text">Đóng (${timeLeft})</span>`,
          showCloseButton: true,
          timer: 10000,
          timerProgressBar: true,
          onOpen: () => {
            timerInterval = setInterval(() => {
              if (timeLeft > 0) timeLeft--;
              const btn = document.getElementById('swal-close-text');
              if (btn) btn.textContent = `Đóng (${timeLeft})`;
            }, 1000);
          },
          onDestroy: () => {
            if (timerInterval) clearInterval(timerInterval);
          }
        });
      }
    );
  }
  
  // Lifecycle Hooks
  // -----------------------------------------------------------------------------------------------------

  /**
   * On init
   */
  ngOnInit(): void {
    this.registerForm = this._formBuilder.group(
      {
        username: ["", [Validators.required, Validators.maxLength(255)]],
        email: ["", [Validators.required, emailValidator]],
        // password: ["", [
        //   Validators.required,
        //   Validators.minLength(6),
        //   Validators.pattern(
        //     /^(?=.*[A-Z])(?=.*[!@#$%^&*()_+{}\[\]:;<>,.?~\\/-]).{6,}$/
        //   ),
        //   Validators.maxLength(255)
        // ]],
        // confPassword: ["", Validators.required],
        agree: [false, Validators.requiredTrue],
        user_type: [null],
        organization_name: ["", [Validators.maxLength(225)]],
        industry: [null],
        scale: [null],
        source: [null],
        product_familiarity: [null],
        description: [null]
      },
      // {
      //   validator: MustMatch("password", "confPassword"),
      // }
    );

    // Nếu có socialUser thì set username và email
    if (this.socialUser) {
      this.registerForm.patchValue({
        username: this.socialUser.name,
        email: this.socialUser.email
      });
      this.is_email_valid = true;
    }

    // Subscribe to config changes
    this._coreConfigService.config
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe((config) => {
        this.coreConfig = config;
      });
  }

  /**
   * On destroy
   */
  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions
    this._unsubscribeAll.next(null);
    this._unsubscribeAll.complete();

    // Clear all data
    if (this.registerForm) {
      this.registerForm.reset();
    }
    this.is_email_valid = false;
    this.wizardStep = 1;
    this.socialUser = null;
  }
}

export function MustMatch(controlName: string, matchingControlName: string) {
  return (formGroup: FormGroup) => {
    const control = formGroup.controls[controlName];
    const matchingControl = formGroup.controls[matchingControlName];
    if (matchingControl?.errors && !matchingControl?.errors?.mustMatch) {
      // return if another validator has already found an error on the matchingControl
      return;
    }
    // set error on matchingControl if validation fails
    if (control.value !== matchingControl.value) {
      matchingControl.setErrors({ mustMatch: true });
    } else {
      matchingControl.setErrors(null);
    }
  };
}