import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { environment } from "environments/environment";
import { BehaviorSubject, Observable } from "rxjs";

@Injectable({
  providedIn: "root",
})
export class DetailWorkSpaceService {
  public isMaximized: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(
    false
  );
  public isNotesCollapsed: BehaviorSubject<boolean> =
    new BehaviorSubject<boolean>(true);
  public isSaveFileFromSearch: BehaviorSubject<boolean> =
    new BehaviorSubject<boolean>(false);
  public shouldToggleNotes: BehaviorSubject<boolean> =
    new BehaviorSubject<boolean>(false);
  public draftContent: BehaviorSubject<string | null> =
    new BehaviorSubject<string | null>(null);
  constructor(private http: HttpClient) {}
  getAllDocumentInWorkSpace(params) {
    return this.http.get<any>(`${environment.apiUrl}/ocr/documents`, {
      params,
    });
  }
  addDocument(body) {
    return this.http.post<any>(`${environment.apiUrl}/ocr/documents`, body);
  }
  rerunDocumemt(fileId) {
    const body = {};
    return this.http.post<any>(
      `${environment.apiUrl}/ocr/documents/${fileId}/rerun`,
      body
    );
  }
  deleteDocument(fileId) {
    return this.http.delete<any>(
      `${environment.apiUrl}/ocr/documents/${fileId}/delete`
    );
  }

  updateDocumentName(documentId: number, newName: string): Observable<any> {
    return this.http.put(
      `${environment.apiUrl}/document/${documentId}/update-name/`,
      { name: newName }
    );
  }
  getDanhSachDieuKhoan(idDocument) {
    return this.http.get<any>(
      `${environment.apiUrl}/ocr/documents/${idDocument}/get_law_clauses`
    );
  }
  getDanhSachDieuKhoanSearch(idDocument) {
    return this.http.get<any>(
      `${environment.apiUrl}/ocr/documents/${idDocument}/get_law_clauses_search`
    );
  }

  addDieuKhoan(idDocument, body) {
    return this.http.post<any>(
      `${environment.apiUrl}/ocr/documents/${idDocument}/law_clauses`,
      body
    );
  }
}
