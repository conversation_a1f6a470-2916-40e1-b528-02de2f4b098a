import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { RouterModule, Routes } from "@angular/router";
import { CoreCommonModule } from "@core/common.module";
import { NgbModule } from "@ng-bootstrap/ng-bootstrap";
import { NgSelectModule } from "@ng-select/ng-select";
import { NgxDatatableModule } from "@swimlane/ngx-datatable";
import { Ng2FlatpickrModule } from "ng2-flatpickr";
import { NgxDocViewerModule } from "ngx-doc-viewer";
import { NgxExtendedPdfViewerModule } from "ngx-extended-pdf-viewer";
import { PipeModule } from "../pipe/pipe.module";
import { ViewDetailFileComponent } from "./view-detail-file.component";
import { BoSungVanBanDieuKhoanModule } from "app/main/apps/quan-ly-van-ban/detail-work-space/bo-sung-van-ban-dieu-khoan/bo-sung-van-ban-dieu-khoan.module";
import { NouisliderModule } from "ng2-nouislider";
import { GraphComponent } from './view-detail-file-graph/view-detail-file-graph.component';
import { SearchCheckboxListComponent } from './view-detail-file-graph/components/search-checkbox-list.component';
import { GraphVisualizationComponent } from './view-detail-file-graph/components/graph/graph-visualization.component';
import { GraphContextMenuComponent } from './view-detail-file-graph/components/graph/graph-context-menu.component';
import { ExpansionModalComponent } from './view-detail-file-graph/components/modal/expansion-modal.component';
import { DocumentDetailsPanelComponent } from './view-detail-file-graph/components/document/document-details-panel.component';
import { DocumentListPanelComponent } from './view-detail-file-graph/components/document/document-list-panel.component';
import { DocumentTableComponent } from './view-detail-file-graph/components/document/document-table.component';

const routes: Routes = [
  {
    path: "detail/:id",
    component: ViewDetailFileComponent,
  },
];
@NgModule({
  declarations: [
    ViewDetailFileComponent,
    GraphComponent,
    SearchCheckboxListComponent,
    GraphVisualizationComponent,
    GraphContextMenuComponent,
    ExpansionModalComponent,
    DocumentDetailsPanelComponent,
    DocumentListPanelComponent,
    DocumentTableComponent,
  ],
  imports: [
    CoreCommonModule,
    CommonModule,
    RouterModule.forChild(routes),
    ReactiveFormsModule,
    FormsModule,
    NgbModule,
    NgxDatatableModule,
    PipeModule,
    Ng2FlatpickrModule,
    NgSelectModule,
    NgxDocViewerModule,
    NgxExtendedPdfViewerModule,
    BoSungVanBanDieuKhoanModule,
    NouisliderModule,
  ],
  exports: [ViewDetailFileComponent, GraphComponent],
})
export class ViewDetailFileModule {}
