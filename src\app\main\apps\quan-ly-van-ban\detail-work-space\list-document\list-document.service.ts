import { Location } from "@angular/common";
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { InterceptorSkipHeader } from "@core/components/loading/loading.interceptor";
import { ShowContent } from "app/models/ShowContent";
import { ShowSideBar } from "app/models/ShowSideBa";
import { environment } from "environments/environment";
import { BehaviorSubject, Observable } from "rxjs";
@Injectable({
  providedIn: "root",
})
export class ListDocumentService {
  private behaviorStack: ShowContent[] = []; // để lưu trạng thái của các trang, để xử lý back lại
  public contentValue: BehaviorSubject<ShowContent> =
    new BehaviorSubject<ShowContent>(ShowContent.Search);
  public rightSideBarValue: BehaviorSubject<ShowSideBar> =
    new BehaviorSubject<ShowSideBar>(ShowSideBar.Note);
  public clauseValue: BehaviorSubject<string> = new BehaviorSubject<string>("");
  public refreshClause: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(
    false
  );
  public FileSearchTemp: BehaviorSubject<any> = new BehaviorSubject<any>(null); // để lưu văn bản được xem từ tìm kiếm, hiển thị trong danh sách tài liệu

  constructor(private http: HttpClient, private location: Location) { }

  setBehavior(newBehavior: ShowContent) {
    const current = this.contentValue.value;
    // // console.log("listStack0", this.behaviorStack);
    // // console.log("newBehavior", newBehavior);

    const last = this.behaviorStack[this.behaviorStack.length - 1];

    // Nếu là Clause và phần tử cuối cũng là Clause, thì không push thêm
    if (!(newBehavior === ShowContent.Clause && last === ShowContent.Clause)) {
      this.behaviorStack.push(current);
    }

    this.contentValue.next(newBehavior);
    // // console.log("listStack1", this.behaviorStack);
  }
  goBack() {
    if (this.behaviorStack.length === 0) {
      this.contentValue.next(ShowContent.Search);
    } else {
      const current = this.contentValue.value;
      const previous = this.behaviorStack.pop();
      if (previous === current && current === ShowContent.Document) {
        this.location.back(); // Quay lại URL trước đó nếu giống nhau
      } else {
        this.contentValue.next(previous);
      }
    }
  }

  reset() {
    this.behaviorStack = [];
    this.contentValue.next(ShowContent.Search);
  }

  getAllDocumentInWorkSpace(params) {
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");

    return this.http.get<any>(`${environment.apiUrl}/ocr/documents`, {
      params: params,
      headers: headers,
    });
  }

  addDocument(body) {
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    return this.http.post<any>(`${environment.apiUrl}/ocr/documents`, body, { headers });
  }
  rerunDocumemt(fileId) {
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");

    const body = {};
    return this.http.post<any>(
      `${environment.apiUrl}/ocr/documents/${fileId}/rerun`,
      body,
      { headers }
    );
  }
  deleteDocument(fileId) {
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    return this.http.delete<any>(
      `${environment.apiUrl}/ocr/documents/${fileId}/delete`,
      { headers }
    );
  }

  updateDocumentName(documentId: number, newName: string): Observable<any> {
    return this.http.put(
      `${environment.apiUrl}/document/${documentId}/update-name/`,
      { name: newName }
    );
  }
  getDanhSachDieuKhoan(idDocument) {
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    return this.http.get<any>(
      `${environment.apiUrl}/ocr/documents/${idDocument}/get_law_clauses`,
      {
        headers: headers,
      }
    );
  }
  getDanhSachDieuKhoanByClauseID(idDocument, clauseId) {
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    return this.http.get<any>(
      `${environment.apiUrl}/ocr/documents/${idDocument}/get_law_clauses_by_id`,
      {
        headers: headers,
        params: { clause_id: clauseId },
      }
    );
  }
  getDanhSachDieuKhoanSearch(idDocument) {
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    return this.http.get<any>(
      `${environment.apiUrl}/ocr/documents/${idDocument}/get_law_clauses_search`,
      {
        headers: headers,
      }
    );
  }
  addDieuKhoan(idDocument, body) {
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    return this.http.post<any>(
      `${environment.apiUrl}/ocr/documents/${idDocument}/law_clauses`,
      body,
      { headers }
    );
  }

  updateClause(body, idClause) {
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    return this.http.put<any>(
      `${environment.apiUrl}/ocr/law_clauses/${idClause}`,
      body,
      { headers }
    );
  }
  deleteClause(idClause) {
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    return this.http.delete<any>(
      `${environment.apiUrl}/ocr/law_clauses/${idClause}`,
      { headers }
    );
  }
  raSoatDieuKhoan(idDocument, body) {
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    return this.http.post<any>(
      `${environment.apiUrl}/ocr/documents/${idDocument}/search_law_clauses`,
      body,
      {
        headers: headers,
      }
    );
  }
  downloadFile(docId: number, fileType: string): Observable<Blob> {
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    return this.http.get(
      `${environment.apiUrl}/document/download?doc_id=${docId}&type=${fileType}`,
      {
        responseType: "blob", // Để nhận file nhị phân
        headers: headers,
      }
    );
  }
  private downloadFileFromSearch(docId: string, fileType: string): Observable<any> {
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    return this.http.get(
      `${environment.apiUrl}/document/download-from-cls-doc?doc_id=${docId}&type=${fileType}`,
      { headers: headers }
    );
  }
  private downloadFileFromS3(docId: string, fileType: string): Observable<any> {
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    return this.http.get(
      `${environment.apiUrl}/document/download-from-s3?doc_id=${docId}&type=${fileType}`,
      { headers: headers }
    );
  }

  getFileReport(docId: string, params: any, body?: any) {
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    return this.http.post<any>(
      `${environment.apiUrl}/ocr/law_clauses/${docId}/get_file_report_compare`,
      body,
      { params, headers }
    );
  }

  getActiveCompareJobs(params: any) {
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    return this.http.get<any>(
      `${environment.apiUrl}/ocr/law_clauses/get_active_compare_jobs`,
      { params, headers }
    );
  }

  checkEmbeddingStatus(docId: string) {
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    return this.http.get<any>(
      `${environment.apiUrl}/ocr/documents/${docId}/embedding-status`,
      { headers }
    );
  }
  saveFile(file, fileType: string): void {
    const nameParts = file.name.split(".");
    nameParts.pop(); // loại bỏ extension cũ
    const fileName = nameParts.join("."); // nối lại tên đầy đủ
    this.downloadFile(file.id, fileType).subscribe(
      (blob) => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `${fileName}.${fileType}`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
      },
      (error) => {
        console.error("Lỗi khi tải file:", error);
        alert("Tải file thất bại!");
      }
    );
  }
  saveFileFromS3(file, fileType: string): Observable<boolean> {
    return new Observable((observer) => {
      this.downloadFileFromS3(file.id, fileType).subscribe(
        async (res: any) => {
          if (!res.url) {
            observer.error("NO_URL");
            return;
          }

          try {
            const response = await fetch(res.url);
            if (!response.ok) {
              observer.error("FETCH_FAILED");
              return;
            }

            const blob = await response.blob();
            const blobUrl = window.URL.createObjectURL(blob);

            const a = document.createElement("a");
            a.href = blobUrl;
            a.download = `${file.title}.${fileType}`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(blobUrl);

            observer.next(true);  // success
            observer.complete();

          } catch (e) {
            observer.error("DOWNLOAD_ERROR");
          }
        },
        () => observer.error("BACKEND_ERROR")
      );
    });
  }


  saveFileSearch(file, fileType: string): void {
    this.downloadFileFromSearch(file.id, fileType).subscribe(
      async (res: any) => {
        if (!res.url) {
          console.error("Không nhận được presigned URL");
          return;
        }

        // mở link S3 → tải trực tiếp (không qua backend)
        // const a = document.createElement("a");
        // a.href = res.url;
        // a.download = `${file.title}.${fileType}`; // thường presigned sẽ override được
        // document.body.appendChild(a);
        // a.click();
        // document.body.removeChild(a);
        try {
          const response = await fetch(res.url);
          if (!response.ok) throw new Error("Failed to fetch file from S3");

          const blob = await response.blob();
          const blobUrl = window.URL.createObjectURL(blob);

          const a = document.createElement("a");
          a.href = blobUrl;
          a.download = `${file.name}.${fileType}`;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);

          window.URL.revokeObjectURL(blobUrl);
        } catch (err) {
          console.error("Lỗi khi tải file:", err);
        }
      },
      (error) => {
        console.error("Lỗi khi tải file:", error);

        if (error.error instanceof Blob) {
          error.error.text().then(text => console.error("Backend error:", text));
        }
      }
    );
  }
  updateOrder(documents: any[]): Observable<any> {
    const url = `${environment.apiUrl}/ocr/documents/swap_order`;
    return this.http.post(url, { documents });
  }
}
