import {
  Component,
  ElementRef,
  Input,
  OnInit,
  Query<PERSON>ist,
  ViewChild,
  ViewChildren,
} from "@angular/core";
import { ShowSideBar } from "app/models/ShowSideBa";
import { environment } from "environments/environment";
import { ToastrService } from "ngx-toastr";
import { Subject } from "rxjs";
import { take, takeUntil } from "rxjs/operators";
import { randomUuidv4 } from "../../../../../../../util/randomUUID";
import { DetailClauseService } from "../detail-clause/detail-clause.service";
import { DetailWorkSpaceService } from "../detail-work-space.service";
import { ListDocumentService } from "../list-document/list-document.service";
import { ComnpareClauseChatbotService } from "./comnpare-clause-chatbot.service";
import { RequestLogService } from "@core/services/request-log.service";

@Component({
  selector: "app-compare-clause-chatbot",
  templateUrl: "./compare-clause-chatbot.component.html",
  styleUrls: ["./compare-clause-chatbot.component.scss"],
})
export class CompareClauseChatbotComponent implements OnInit {
  @ViewChild("chatContainer") private chatContainer!: ElementRef;
  @ViewChildren("editable") editableSpans!: QueryList<
    ElementRef<HTMLSpanElement>
  >;
  @Input() isInModal: boolean = false; // Để kiểm tra xem component đang ở modal hay sidebar

  messages = [
    {
      id: new randomUuidv4().randomUuidv4(),
      thinking: "",
      reason: "",
      conclusion: "",
      solution: "",
      question: "",
      role: "",
    },
  ];
  userInput = "";
  public isMaximized: boolean = false;
  public thinkingText: string = "";
  public conclusionText: string = "";
  public reasonText: string = "";
  public solutionText: string = "";
  public idMessage: number = 0;
  public doneChatBot: boolean = false;
  public contentEditable: boolean = false;
  public bodyChat: {
    item1: any;
    item2: any;
  } = {
    item1: {},
    item2: null,
  };
  public bodyMessage = {
    item1: {},
    item2: {},
    messages: [],
  };
  abortController: AbortController | null = null;

  public unSubAll: Subject<any> = new Subject();
  constructor(
    private workSpaceService: DetailWorkSpaceService,
    private compareChatbotService: ComnpareClauseChatbotService,
    private listDocumentservice: ListDocumentService,
    private detailClause: DetailClauseService,
    private toast: ToastrService,
    private reqLog: RequestLogService
  ) {}

  ngOnInit(): void {
    this.workSpaceService.isMaximized
      .pipe(takeUntil(this.unSubAll))
      .subscribe((res) => {
        this.isMaximized = res;
      });
    this.compareChatbotService.clauseInfo1
      .pipe(takeUntil(this.unSubAll))
      .subscribe((res) => {
        this.messages = [
          {
            id: new randomUuidv4().randomUuidv4(),
            thinking: "",
            reason: "",
            conclusion: "",
            solution: "",
            question: "",
            role: "",
          },
        ];
        this.bodyChat.item1 = res;
      });
    this.compareChatbotService.clauseInfo2
      .pipe(takeUntil(this.unSubAll))
      .subscribe((res) => {
        this.bodyChat.item2 = res;
        if (res) {
          if (res.ly_do == null) {
            this.messages = [
              {
                id: new randomUuidv4().randomUuidv4(),
                thinking: "",
                reason: "",
                conclusion: "",
                solution: "",
                question: null,
                role: "assistant",
              },
            ];
            // this.streamFromChatbot();
          } else {
            this.messages = [
              {
                id: new randomUuidv4().randomUuidv4(),
                thinking: res.tien_trinh_tu_duy,
                reason: res.ly_do,
                conclusion: res.ket_luan,
                solution: res.giai_phap,
                question: null,
                role: "assistant",
              },
            ];
          }
        } else {
          this.cancelRequest();
        }
      });
    this.compareChatbotService.clauseTerm
      .pipe(takeUntil(this.unSubAll))
      .subscribe((res) => {
        if (res) {
          if (res == "x") {
            // để k bị so sánh lại
          } else {
            this.messages = res;
            this.cancelRequest();
          }
        } else {
          this.streamFromChatbot();
        }
      });
  }

  async streamFromChatbot() {
    const userInfor = JSON.parse(localStorage.getItem("current_User"));
    this.abortController = new AbortController();
    this.doneChatBot = false;
    let fullResponseText = "";

    try {
      this.bodyMessage.item1 = this.bodyChat.item1;
      this.bodyMessage.item2 = this.bodyChat.item2;
      this.bodyMessage.messages = this.messages.slice(1).map((msg) => ({
        role: msg.role,
        content:
          msg.role == "assistant"
            ? `${msg.reason}</reason>${msg.conclusion}</conclusion>${msg.solution}</solution>`
            : this.userInput,
      }));
      const postBody =
        this.messages.length == 1 ? this.bodyChat : this.bodyMessage;

      const response = await fetch(`${environment.apicompare}/compare`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("token")}`,
        },
        body: JSON.stringify(postBody),
      });
      
      if (!response.body) {
        console.error("❌ No response body");
        this.reqLog
          .ingest({
            user_id: userInfor.id,
            feature_name: "compare",
            method: "POST",
            status_code: response.status,
            response: "",
            parameters: this.reqLog.slimCompareParams(postBody),
          })
          .pipe(take(1))
          .subscribe();
        return;
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder("utf-8");
      let buffer = "";
      
      // Sử dụng một flag duy nhất để track section hiện tại
      let currentSection: 'thinking' | 'conclusion' | 'reason' | 'solution' | 'none' = 'none';

      let thinkingText = "";
      let conclusionText = "";
      let reasonText = "";
      let solutionText = "";
      let id = "";

      while (true) {
        if (this.abortController.signal.aborted) {
          this.doneChatBot = true;
          break;
        }
        
        const { value, done } = await reader.read();
        if (done) {
          this.reqLog
            .ingest({
              user_id: userInfor.id,
              feature_name: "compare",
              method: "POST",
              status_code: response.status,
              response: fullResponseText,
              parameters: this.reqLog.slimCompareParams(postBody),
            })
            .pipe(take(1))
            .subscribe();

          this.detailClause.idClause.next(null);
          this.doneChatBot = true;
          this.compareChatbotService.emitSingleCompareFinished(
            this.bodyChat.item2.id,
            true
          );
          this.saveResultCompare();
          break;
        }

        const chunk = decoder.decode(value, { stream: true });
        fullResponseText += chunk;
        buffer += chunk;

        const [jsonObjects, remaining] = this.extractJsonObjects(buffer);
        buffer = remaining;
        
        for (const obj of jsonObjects) {
          if (obj.id) {
            id = obj.id;
          }
          
          const text = obj.text || "";
          
          // QUAN TRỌNG: Check closing tag TRƯỚC, opening tag SAU
          // Check nếu text chứa closing tags
          if (text.includes('</think>')) {
            currentSection = 'none';
            continue; // Skip text này, không append
          }
          if (text.includes('</conclusion>')) {
            currentSection = 'none';
            continue;
          }
          if (text.includes('</reason>')) {
            currentSection = 'none';
            continue;
          }
          if (text.includes('</solution>')) {
            currentSection = 'none';
            continue;
          }
          
          // Check nếu text chứa opening tags
          if (text.includes('<think>') || this.checkStartThinking(text)) {
            currentSection = 'thinking';
            // Lấy phần text sau tag nếu có
            const startIdx = text.indexOf('<think>');
            if (startIdx !== -1 && startIdx + 7 < text.length) {
              thinkingText += text.substring(startIdx + 7);
            }
            continue;
          }
          if (text.includes('<conclusion>') || this.checkStartConclusion(text)) {
            currentSection = 'conclusion';
            const startIdx = text.indexOf('<conclusion>');
            if (startIdx !== -1 && startIdx + 12 < text.length) {
              conclusionText += text.substring(startIdx + 12);
            }
            continue;
          }
          if (text.includes('<reason>') || this.checkStartReason(text)) {
            currentSection = 'reason';
            const startIdx = text.indexOf('<reason>');
            if (startIdx !== -1 && startIdx + 8 < text.length) {
              reasonText += text.substring(startIdx + 8);
            }
            continue;
          }
          if (text.includes('<solution>') || this.checkStartSolution(text)) {
            currentSection = 'solution';
            const startIdx = text.indexOf('<solution>');
            if (startIdx !== -1 && startIdx + 10 < text.length) {
              solutionText += text.substring(startIdx + 10);
            }
            continue;
          }

          // Append text vào section hiện tại
          switch (currentSection) {
            case 'thinking':
              thinkingText += text;
              break;
            case 'conclusion':
              conclusionText += text;
              break;
            case 'reason':
              reasonText += text;
              break;
            case 'solution':
              solutionText += text;
              break;
          }

          this.thinkingText = thinkingText;
          this.conclusionText = conclusionText;
          this.reasonText = reasonText;
          this.solutionText = solutionText;

          // Update to message list
          const index = this.messages.findIndex((m) => m.id === id);
          if (index !== -1) {
            this.messages[index].thinking = this.thinkingText;
            this.messages[index].conclusion = this.conclusionText;
            this.messages[index].solution = this.solutionText;
            this.messages[index].reason = this.reasonText;
            this.messages[index].role = "assistant";
          } else {
            this.messages.push({
              id: id,
              thinking: this.thinkingText,
              conclusion: this.conclusionText,
              solution: this.solutionText,
              reason: this.reasonText,
              question: null,
              role: "assistant",
            });
          }
        }
        this.scrollToBottom();
      }
    } catch (error) {
      console.error("🔥 Fetch error:", error);
      this.reqLog
        .ingest({
          user_id: userInfor.id,
          feature_name: "compare",
          method: "POST",
          status_code: 0,
          response: (error?.message ?? "fetch_error").toString(),
          parameters: this.reqLog.slimCompareParams(
            this.messages.length == 1 ? this.bodyChat : this.bodyMessage
          ),
        })
        .pipe(take(1))
        .subscribe();
      this.doneChatBot = true;
      this.detailClause.idClause.next(null);
      this.toast.error(
        "Có lỗi xảy ra khi kết nối đến chatbot. Vui lòng thử lại sau.",
        "Lỗi kết nối",
        {
          closeButton: true,
          positionClass: "toast-top-right",
          toastClass: "toast ngx-toastr",
        }
      );
    } finally {
      this.abortController = null;
    }
  }

  extractJsonObjects(str: string): [any[], string] {
    let objects = [];
    let depth = 0;
    let start = -1;

    for (let i = 0; i < str.length; i++) {
      if (str[i] === "{") {
        if (depth === 0) start = i;
        depth++;
      } else if (str[i] === "}") {
        depth--;
        if (depth === 0 && start !== -1) {
          const jsonStr = str.slice(start, i + 1);
          try {
            objects.push(JSON.parse(jsonStr));
          } catch (e) {
            console.warn("⚠️ JSON parse failed", jsonStr);
          }
          start = -1;
        }
      }
    }

    const remaining = depth > 0 && start !== -1 ? str.slice(start) : "";
    return [objects, remaining];
  }
  checkStartThinking(text) {
    if (text.includes("<think>")) return true;
    else if (text.includes("<reason>")) return false;
  }
  checkStartReason(text) {
    if (text.includes("<reason>")) return true;
    else if (text.includes("</reason>")) return false;
  }
  checkStartConclusion(text) {
    if (text.includes("<conclusion>")) return true;
    else if (text.includes("</conclusion>")) return false;
  }
  checkStartSolution(text) {
    if (text.includes("<solution>")) return true;
    else if (text.includes("</solution>")) return false;
  }

  minimumChatbot() {
    this.isMaximized = false;
    this.workSpaceService.isMaximized.next(false);
  }
  sendMessage(event: KeyboardEvent) {
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault(); // Ngăn xuống dòng khi nhấn Enter
      if (this.userInput.trim() && this.doneChatBot) {
        this.messages.push({
          id: new randomUuidv4().randomUuidv4(),
          thinking: "",
          conclusion: null,
          solution: null,
          reason: null,
          question: this.userInput,
          role: "user",
        });
        this.streamFromChatbot();
        this.userInput = "";
        this.scrollToBottom();
      }
      this.resetTextareaHeight(); // 👉 Gọi reset chiều cao
    }
  }
  resetTextareaHeight() {
    const textarea = document.getElementById(
      "queryChatbot"
    ) as HTMLTextAreaElement;
    if (textarea) {
      textarea.style.height = "auto"; // Reset lại chiều cao mặc định
    }
  }
  autoResize(event: Event) {
    const textarea = event.target as HTMLTextAreaElement;
    textarea.style.height = "auto"; // Reset height
    textarea.style.height = textarea.scrollHeight + "px"; // Set to scroll height
  }
  scrollToBottom(): void {
    try {
      this.chatContainer.nativeElement.scrollTop =
        this.chatContainer.nativeElement.scrollHeight;
    } catch (err) {
      console.error("Scroll failed:", err);
    }
  }
  addNewChat() {
    this.messages = []; // Clear messages for new chat
  }

  closeCompareChatbot() {
    // this.listDocumentservice.rightSideBarValue.next(ShowSideBar.Chatbot);
    this.isMaximized = false;
  }

  saveResultCompare() {
    const result: any[] = [];
    this.editableSpans.forEach((ref) => {
      const index = +ref.nativeElement.getAttribute("data-index")!;
      const type = ref.nativeElement.getAttribute("data-type");
      const content = ref.nativeElement.innerHTML.trim();

      if (!result[index]) {
        result[index] = { index };
      }

      result[index][type!] = content;
    });

    // Lọc ra các message hợp lệ (đã có ít nhất 1 trường)
    const validMessages = result.filter(
      (msg) => msg.reason || msg.conclusion || msg.solution || msg.thinking
    );

    // Lấy 3 message cuối cùng
    const lastThree = validMessages.slice(-3);
    const formData = new FormData();
    const lastMessageUpdate = lastThree[lastThree.length - 1];
    formData.append("ly_do", lastMessageUpdate.reason);
    formData.append("ket_luan", lastMessageUpdate.conclusion);
    formData.append("giai_phap", lastMessageUpdate.solution);
    formData.append("tien_trinh_tu_duy", lastMessageUpdate.thinking);
    // console.log(lastMessageUpdate);

    if (formData) {
      const id = this.bodyChat.item2.id;
      this.compareChatbotService
        .saveResoultCompare(id, formData)
        .subscribe((res) => {
          this.detailClause.isSaveResoultFromChatBot.next(true);
          this.contentEditable = false;
        });
    }
  }

  editCompareChatbot(index) {
    const span = this.editableSpans.toArray()[index];
    if (span) {
      span.nativeElement.focus();
    }
  }
  showScrollButton = false;

  checkScrollButtonVisibility(): void {
    const element = this.chatContainer.nativeElement;
    const threshold = 100;
    const position = element.scrollTop + element.clientHeight;
    const height = element.scrollHeight;

    // Nếu người dùng chưa scroll đến cuối => hiện nút
    this.showScrollButton = position + threshold < height;
  }
  cancelMessage() {
    this.doneChatBot = true;
  }
  getConclusionClass(conclusion: string): string {
    const normalized = conclusion?.trim();
    if (normalized === "Không mâu thuẫn") {
      return "badge badge-pill badge-success";
    } else if (normalized === "Phát hiện mâu thuẫn") {
      return "badge badge-pill badge-danger";
    }
    return ""; // fallback
  }
  cancelRequest() {
    this.doneChatBot = true;
    if (this.abortController) {
      this.abortController.abort();
    }
  }
  ngAfterViewInit() {
    this.chatContainer.nativeElement.addEventListener("scroll", () => {
      this.checkScrollButtonVisibility();
    });
  }
  ngOnDestroy() {
    this.compareChatbotService.clauseTerm.next(this.messages);
    this.detailClause.isSaveResoultFromChatBot.next(false);
    this.detailClause.idClause.next(null);
    // this.compareChatbotService.clauseInfo2.next(null);
    // this.workSpaceService.isNotesCollapsed.next(true); // đóng phần collapse khi destroy chatbot compare
    this.unSubAll.next(null);
    this.unSubAll.complete();
  }
}
