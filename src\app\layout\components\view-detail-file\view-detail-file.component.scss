/* Container c<PERSON><PERSON> bảng */
.class-85vh {
  height: 85vh;
}

.document-table-container {
  width: 100%;
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
  height: 70vh;
  font-family: Arial, sans-serif;
}

/* C<PERSON><PERSON> hình bảng */
.document-table {
  width: 100%;
  border-collapse: collapse;
}

.document-table td {
  padding: 8px 12px;
  border: 1px solid #ddd;
}

/* Cột tiêu đề */
.document-table__label {
  background-color: #f8f9fa;
  font-weight: bold;
  width: 25%;
}

/* Cột nội dung */
.document-table__value {
  width: 25%;
}

.document-table__value2 {
  width: 100%;
}

/* Trạng thái hiệu lực - <PERSON><PERSON>u sắc theo status */
.document-status {
  font-weight: bold;
  padding: 10px;
  border-radius: 4px;
}

/* Còn hiệu lực */
.document-status--active {
  background-color: rgba(220, 255, 220, 1);
  color: rgba(25, 135, 84, 1);
}

/* Hết hi<PERSON>u lực một phần */
.document-status--warning {
  background-color: rgba(255, 245, 200, 1);
  color: rgba(255, 165, 0, 1);
}

/* Hết hiệu lực toàn bộ */
.document-status--danger {
  background-color: rgba(255, 230, 230, 1);
  color: rgba(220, 53, 69, 1);
}

/* Màu mặc định nếu không khớp với các trạng thái trên */
.document-status--info {
  background-color: rgba(230, 245, 255, 1);
  color: rgba(13, 110, 253, 1);
}

.document-status__label {
  margin-right: 5px;
}

/* Trạng thái hiệu lực - Màu sắc theo status */
.document-status__value {
  font-weight: bold;
  padding: 6px 12px;
  border-radius: 4px;
}

.header-luocdo {
  background-color: rgba(245, 245, 245, 1);
}

::ng-deep.accordion .card .card-header button {
  background-color: rgba(245, 245, 245, 1) !important;
}

.doc-container {
  position: relative;
}

.loading-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(255, 255, 255, 0.8);
  padding: 10px 20px;
  border-radius: 5px;
  font-size: 16px;
  font-weight: bold;
}

#detail-file .accordion .card .card-header button {
  background: rgba(245, 245, 245, 1) !important;
}

.invalid-date-tong-quan {
  background-color: #ffe6e6;
}

@keyframes highlightFade {
  0% {
    background-color: yellow;
  }

  100% {
    background-color: transparent;
  }
}

.highlight-scroll {
  animation: highlightFade 4s ease forwards;
}

.font-weight-bold {
  font-weight: bold !important;
}

:host [contenteditable="true"] {
  border: 1px dashed #ccc !important;
  padding: 4px !important;
  min-height: 24px !important;
}

.popover-box {
  position: absolute;
  background-color: white;
  border: 1px solid #ccc;
  // padding: 6px 10px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  z-index: 999;
}

.function-item {
  padding: 10px 25px;
}

.function-item:hover {
  background-color: #f0f0f0;
}

.gradient-text {
  background: linear-gradient(to left,
      rgba(0, 97, 255) 0%,
      rgba(0, 97, 255, 0.8) 45%,
      rgb(0, 97, 255, 0) 50%,
      rgba(0, 97, 255, 0.8) 55%,
      rgba(0, 97, 255) 100%);
  background-size: 200% auto;
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  -webkit-text-fill-color: transparent;
  text-fill-color: transparent;
  animation: textShine 3s linear infinite;
}

@keyframes textShine {
  0% {
    background-position: -100% center;
  }

  100% {
    background-position: 100% center;
  }
}

.view-detail-file-header-border {
  border-bottom: 1px solid rgba(224, 224, 224, 1);
}

.view-detail-file-save-button {
  width: 80px;
  padding: 0.3rem 0;
}
.view-actions {
  display: flex;
  align-items: center;
  column-gap: 0.5rem;
  flex-shrink: 0;

  .action-pill {
    border-radius: 999px;
    padding: 0.3rem 1.1rem;
    font-size: 0.8125rem;
    white-space: nowrap;
  }
}

.spinner-saving {
  max-height: 29.6px;
}

.view-detail-file-content-container {
  height: 80vh;
  display: flex;
}

.view-detail-file-content-wrapper {
  position: relative;
  overflow-y: auto;
  overflow-x: hidden;
  flex: 1 1 auto;
  background: linear-gradient(to bottom,
      #e6f0f8 0px,
      #ffffff 80px,
      #ffffff);
}

.view-detail-file-popover {
  position: absolute;
  z-index: 9999;
}

.view-detail-file-doc-viewer {
  height: 75vh;
}

.view-detail-file-summary-wrapper {
  position: relative;
  overflow-y: auto;
  overflow-x: hidden;
  background: linear-gradient(to bottom,
      #e6f0f8 0px,
      #ffffff 80px,
      #ffffff);
}

.view-detail-file-summary-content {
  white-space: pre-wrap;
  word-wrap: break-word;
}



.view-detail-file-summary-content p {
  margin: 0;
  /* thu nhỏ khoảng cách giữa các mục */

}


.view-detail-file-luocdo-container {
  height: 80vh;
  display: flex;
  flex-direction: column;
}

.view-detail-file-luocdo-content {
  flex: 1 1 auto;
  overflow-y: auto;
  overflow-x: hidden;
}

.view-detail-file-lienquan-container {
  height: 75vh;
  display: flex;
  flex-direction: column;
}

.view-detail-file-lienquan-content {
  overflow-y: auto;
  overflow-x: hidden;
  flex: 1 1 auto;
}

.view-detail-file-table-container {
  overflow: auto;
  max-height: 70vh;
}

.view-detail-file-dothi-container {
  // height: 80vh;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  overflow-x: hidden;
}

.custom-tooltip {
  // background: rgba(0, 0, 0, 0.85);
  // color: #fff;
  background: white;
  color: #212121;
  border: 1px solid #ccc;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 13px;
  max-width: 450px;
  word-wrap: break-word;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  opacity: 0.99;
  line-height: 1.4;
  transition: opacity 0.2s ease-in-out;
  display: flex;
  flex-direction: column;
}

.custom-tooltip> :not(.tooltip-hint) {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  // white-space: normal;
  white-space: pre-line;
}

.tooltip-content-relation {
  font-weight: bold;

  >ul {
    font-weight: normal;
  }
}

.tooltip-hint {
  margin-top: 4px;
  font-size: 12px;
  color: #727272;
  font-style: italic;
  flex-shrink: 0;
}

.custom-tooltip-relation {
  // background: rgba(0, 0, 0, 0.85);
  // color: #fff;
  background: white;
  color: #212121;
  border: 1px solid #ccc;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 13px;
  max-width: 450px;
  word-wrap: break-word;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  opacity: 0.99;
  line-height: 1.4;
  transition: opacity 0.2s ease-in-out;
  display: flex;
  flex-direction: column;
}

.custom-tooltip-detail {
  // background: rgba(0, 0, 0, 0.85);
  // color: #fff;
  background: white;
  color: #212121;
  border: 1px solid #ccc;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 13px;
  max-width: 450px;
  word-wrap: break-word;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  opacity: 0.99;
  line-height: 1.4;
  transition: opacity 0.2s ease-in-out;
  display: flex;
  flex-direction: column;
  white-space: pre-line;
}

.reference-modal-body .reference-content .accordion .card {
  box-shadow: none;
}

.reference-modal-body .reference-content .accordion .card .card-header svg {
  min-height: 24px;
  min-width: 24px;
}

.reference-modal-body .reference-content .accordion .card .card-header button {
  padding: 0 1rem 0 0;
}

.reference-modal-body .reference-content .accordion .card .card-header span {
  padding: .5rem 0;
}

.reference-modal-body .reference-content .accordion .card-body {
  padding: 0;
}

.reference-modal-body .reference-content .accordion .ref-title {
  color: black;
}

.reference-modal-body .reference-content .accordion .ref-content {
  padding: .5rem;
  border: 1px solid #616161;
  border-radius: 6px;
}

.reference-modal-body .reference-content .accordion .rel-content {
  padding: .5rem;
  border: 1px solid #616161;
  border-radius: 6px;
  flex: 1;
}

.reference-modal-body .reference-content .accordion .rel-content p {
  margin: .5rem 0 0 0;
}

.ref-text-scroll {
  text-decoration: underline !important;
}

.text-relation-title {
  color: #212121;
}

.tooltip-list {
  margin: 0;
}

.bookmark-base-temp {
  border: none;
  display: inline-block;
  padding: 0.3rem 0.5rem;
  font-size: 84%;
  font-weight: 600;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 10rem;
}

.text-color-dinh_chinh {
  color: #DC3545 !important;
}

.bookmark-dinh_chinh {
  @extend .bookmark-base-temp;
  background-color: #DC3545;
  color: white;
}

.text-color-sua_doi_bo_sung {
  color: #FD7E14 !important;
}

.bookmark-sua_doi_bo_sung {
  @extend .bookmark-base-temp;
  background-color: #FD7E14;
  color: #fff !important;
}

.text-color-thay_the {
  color: #FDC193 !important;
}

.bookmark-thay_the {
  @extend .bookmark-base-temp;
  background-color: #FFF2E8;
  color: #FD7E14;
}

.text-color-huong_dan {
  color: #198754 !important;
}

.bookmark-huong_dan {
  @extend .bookmark-base-temp;
  background-color: #198754;
  color: white;
}

.text-color-quy_dinh_chi_tiet {
  color: #198754 !important;
}

.bookmark-quy_dinh_chi_tiet {
  @extend .bookmark-base-temp;
  background-color: #198754;
  color: white;
}

.text-color-bai_bo {
  color: #9E9E9E !important;
}

.bookmark-bai_bo {
  @extend .bookmark-base-temp;
  background-color: #9E9E9E;
  color: white;
}

.text-color-bai_bo_cum_tu {
  color: #9747FF !important;
}

.bookmark-bai_bo_cum_tu {
  @extend .bookmark-base-temp;
  background-color: #9747FF;
  color: white;
}

.text-color-dan_chieu {
  color: #3B82F6 !important;
}

.bookmark-dan_chieu {
  @extend .bookmark-base-temp;
  background-color: #3B82F6;
  color: white;
}

.graph-empty-state {
  height: 80vh;
}

.ref-title-show-detail-icon {
  cursor: pointer;
  // width: 26px;
  height: 26px;
  background-color: #f0f0f0;
  padding: 0 8px;
  color: #212121;
}

.ref-title-show-detail-icon:hover {
  background-color: #e0e0e0;
}

.rel-title-show-detail-icon {
  cursor: pointer;
  // width: 22px;
  height: 22px;
  background-color: #f0f0f0;
  padding: 0 8px;
  color: #212121;
}

.rel-title-show-detail-icon:hover {
  background-color: #e0e0e0;
}
.hidden-nav {
  display: none !important;
}

.view-detail-file-content-wrapper.no-draft-bg,
.view-detail-file-summary-wrapper.no-draft-bg {
  background: #ffffff !important;
}
.chatbot-doc-no-frame {
  border: none !important;
  box-shadow: none !important;
  background-color: #fff !important;
}

.chatbot-doc-no-frame .card,
.chatbot-doc-no-frame .card-body {
  border: none !important;
  box-shadow: none !important;
}

.view-actions {
  .action-pill {
    border-radius: 999px;
    padding: 0.25rem 0.9rem;
    font-size: 0.8125rem;
  }
}

.view-detail-file-header-border > .w-100.d-flex:not(.view-detail-file-header-main) {
  flex-wrap: wrap;
}

.view-detail-file-header-border .one-line {
  flex: 1 1 auto;
  min-width: 0;
}
.view-detail-file-content-wrapper.chatbot-doc-no-frame {
  border: none !important;
  box-shadow: none !important;
  background-color: transparent !important;
}

.no-draft-bg {
  background-color: transparent !important;
}
/* Container của title + group nút */
.view-detail-file-header-main {
  display: flex;
  align-items: center;
  justify-content: space-between;

  /* BẮT BUỘC: không cho flex item (title / actions) xuống dòng */
  flex-wrap: nowrap;
}

/* Title: chiếm phần còn lại, tối đa 2 dòng + ... */
.view-detail-file-title {
  flex: 1 1 auto;     // cho phép co giãn
  min-width: 0;       // để text chấp nhận bị cắt
  margin-right: 1rem; // tạo khoảng cách với nút

  /* override class .one-line cũ */
  white-space: normal !important;
  overflow: hidden;
  text-overflow: ellipsis;

  /* multi-line ellipsis 2 dòng */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* Nhóm nút: không bị co, không bị đẩy xuống dòng */
.view-actions {
  display: flex;
  align-items: center;
  flex-shrink: 0;  // giữ nguyên độ rộng, không cho flex ép nhỏ
  gap: 0.5rem;     // khoảng cách giữa các nút
}

/* Nếu ở đâu đó anh có style đặt flex-wrap: wrap; cho header thì override lại */
.view-detail-file-header-border {
  flex-wrap: nowrap;
}
