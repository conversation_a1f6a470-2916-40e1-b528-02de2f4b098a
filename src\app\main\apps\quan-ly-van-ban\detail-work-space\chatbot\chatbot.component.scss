@import "bootstrap/scss/functions";
@import "bootstrap/scss/variables";

:host ::ng-deep .answer table {
  border-collapse: collapse;
  width: 100%;
}

:host ::ng-deep .answer th,
:host ::ng-deep .answer td {
  border: 1px solid #ccc;
  padding: 8px;
}

.container-chatbot {
  background-color: #fff;
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  min-height: 0;

  &.maximized {
    flex-direction: row;
    height: 100vh;
    min-height: 0;
  }
}

.maximized {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

/* Mobile Menu Toggle Button */
.mobile-menu-toggle {
  display: none;
  position: fixed;
  top: 15px;
  left: 15px;
  z-index: 1001;
  background-color: rgb(212, 212, 212);
  border: none;
  border-radius: 8px;
  padding: 10px 12px;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;

  &:hover {
    background: rgb(212, 212, 212);
    transform: scale(1.05);
  }

  &.menu-open {
    left: 295px;
  }

  i {
    color: white;
    font-size: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

/* Mobile Sidebar Overlay */
.mobile-sidebar-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

/* Mobile Sidebar Close Button */
.mobile-sidebar-close {
  display: none;
  position: absolute;
  top: 16px;
  right: 10px;
  background: transparent;
  border: none;

  cursor: pointer;
  z-index: 1002;
  width: 24px;
  height: 24px;

  i {
    color: #6e6b7b;
    font-size: 24px;
  }
}

.badge-container {
  gap: 2px;
}

.context-menu {
  position: fixed;
  background: #fff;
  z-index: 9999;
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.23);
  border-radius: 8px;
  padding: 4px 0;
}

.context-menu ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.context-menu li {
  padding: 8px 12px;
  cursor: pointer;
}

.context-menu li:hover {
  background-color: #f0f0f0;
}

.nav .dropdown-toggle:not(.active)::after {
  display: none;
}

.expand-button:hover {
  background-color: rgba(238, 238, 238, 1);
}

.document-info {
  flex: 1;
  max-width: 85%;
}

.document-meta {
  font-size: 14px;
  color: #666;
}

.document-title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 95%;
}

.document-title {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-grow: 1;
  min-width: 0;
}

.action-file {
  flex-shrink: 0;
  cursor: pointer;
}

.chat-content {
  flex: 1 1 auto;
  overflow-y: auto;
  padding: 12px;
  min-height: 0;
  flex: 1 1 0;
  min-height: 0;
  overflow-y: auto;
}

.chat-content-custom {
  max-height: 70%;
}

:host ::ng-deep a {
  color: #007bff !important;
}

.message-bubble {
  margin-bottom: 10px;
  border-radius: 16px;
  line-height: 1.4;
  position: relative;
}

.answer.bot {
  color: #000;
  align-self: flex-start;
  font-size: 1rem !important;
}

.answer.user {
  background-color: rgba(245, 245, 245, 1);
  padding: 12px 16px;
  border-radius: 24px;
  color: #333;
  width: fit-content;
  margin-left: auto;
  word-wrap: break-word;
  display: flex;
  flex-direction: column;

  p {
    margin: 0;
  }
}

:host .answer.user ::ng-deep p {
  margin: 0 !important;
}

.chat-input {
  padding: 10px;
  border-top: 1px solid #eee;
  background-color: #fff;
  max-height: 300px;
  overflow-y: auto;
}

.chat-input input {
  flex: 1;
  padding: 10px 12px;
  border-radius: 8px;
  border: 1px solid #ccc;
  outline: none;
  margin-right: 8px;
}

.btn-send {
  padding: 8px 16px;
  border: none;
  background-color: #007bff;
  color: white;
  border-radius: 8px;
  cursor: pointer;
}

.btn-send:hover {
  background-color: #0056b3;
}

::ng-deep.tab-content>.active {
  height: 100% !important;
}

.button-add-conversation-minimized {
  border-radius: 4px;
  padding: 6px 0;
  cursor: pointer;
  position: absolute;
  // top: -50px;
  top: -45px;
  right: 50px;

  @media (max-width: 991.98px) {
    right: 140px;
  }
}

.button-add-conversation {
  border-radius: 4px;
  padding: 6px;
  cursor: pointer;
}

.gradient-text {
  background: linear-gradient(to left,
      rgba(0, 97, 255) 0%,
      rgba(0, 97, 255, 0.8) 45%,
      rgb(0, 97, 255, 0) 50%,
      rgba(0, 97, 255, 0.8) 55%,
      rgba(0, 97, 255) 100%);
  background-size: 200% auto;
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  -webkit-text-fill-color: transparent;
  text-fill-color: transparent;
  animation: textShine 3s linear infinite;
}

// .thinking {
//   background: linear-gradient(
//   to left,
//   rgba(0, 0, 0, 1) 0%,
//   rgba(0, 0, 0, 0.8) 45%,
//   rgba(0, 0, 0, 0) 50%,
//   rgba(0, 0, 0, 0.8) 55%,
//   rgba(0, 0, 0, 1) 100%
// );

//   background-size: 200% auto;
//   background-clip: text;
//   -webkit-background-clip: text;
//   color: transparent;
//   -webkit-text-fill-color: transparent;
//   text-fill-color: transparent;
//   animation: textShine 3s linear infinite;
// }
@keyframes textShine {
  0% {
    background-position: -100% center;
  }

  100% {
    background-position: 100% center;
  }
}

::ng-deep #chatbot .accordion .card .card-header button {
  padding: 0 !important;
}

:host ::ng-deep {

  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  .h1,
  .h2,
  .h3,
  .h4,
  .h5,
  .h6 {
    font-weight: 600 !important;
  }
}

span[contenteditable="true"] {
  padding: 4px !important;
  min-height: 36px !important;
  display: inline-block;
  width: 100%;
  border: none;
}

span[contenteditable="true"]:focus {
  border: none;
  outline: none;
}

.scroll-to-bottom {
  bottom: 100px;
  background-color: #fff;
  z-index: 10;
  align-self: center;
  box-shadow: -1px 4px 16px 0px rgba(0, 0, 0, 0.15);
  border-radius: 50%;
  padding: 5px;
  cursor: pointer;
}

.scroll-to-bottom-maximized {
  bottom: 100px;
  background-color: #fff;
  right: 40%;
  position: fixed;
  z-index: 10;
  display: flex;
  box-shadow: -1px 4px 16px 0px rgba(0, 0, 0, 0.15);
  border-radius: 50%;
  padding: 5px;
  cursor: pointer;
}

.chat-content-maximized {
  max-height: 100% !important;
  max-width: 50vw;
  font-size: 14px;
}

@media (max-width: 1200px) {
  .chat-content-maximized {
    max-width: 60vw;
  }
}

.button-add-conversation:hover {
  background-color: rgba(238, 238, 238, 1);
}

.coversation {
  border-radius: 8px;
}

.coversation:hover {
  background-color: rgb(221, 221, 221);
  cursor: pointer;
}

.coversation.selected {
  background-color: rgba(238, 238, 238, 1);
}

.conversation {
  color: rgba(33, 37, 41, 1);
  border-radius: 4px;
}

.auto-resize-textarea {
  overflow: hidden;
  resize: none;
  max-height: 200px;
}

.chat-input-wrapper {
  display: flex;
  padding: 8px 12px;
  border: 1px solid rgba(224, 224, 224, 1);
  border-radius: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chat-input {
  border: none;
  outline: none;
  resize: none;
  flex: 1;
  line-height: 1.5;
  padding: 6px 10px;
  background-color: transparent;
  padding-top: 9px;
}

.chat-button-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.chat-send-button,
.chat-cancel-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  background-color: #4285f4;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.chat-cancel-button {
  background-color: #f44336;
}

.chat-send-button svg,
.chat-cancel-button svg {
  width: 20px;
  height: 20px;
}

:host ::ng-deep ol {
  padding-left: 12px !important;
}

.coversation-contain {
  padding: 8px;
  overflow-y: auto;
  flex: 1 1 auto;
}

.fly-up {
  animation: flyUpFade 1.2s ease-out forwards;
}

@keyframes flyUpFade {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }

  30% {
    opacity: 1;
    transform: translateY(0px);
  }

  100% {
    opacity: 0;
    transform: translateY(-20px);
  }
}

.disabled-button {
  background-color: rgba(158, 158, 158, 1);
  cursor: not-allowed;
}

.chat-input-container {
  width: 100%;
  background: #fff;
  flex-shrink: 0;
  max-width: 50vw;
  align-self: center;
}

.chat-main-flex {
  display: flex;
  flex-direction: column;
  flex: 1 1 0;
  min-width: 0;
  min-height: 0;

  @media (max-width: 991.98px) {
    justify-content: center; // canh giữa theo chiều dọc
    align-items: center; // canh giữa theo chiều ngang (nếu muốn)
  }
}

.centered-input {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 90%;
  padding-bottom: 60px;
}

.input-container {
  padding: 8px;
  border-radius: 24px;
  border: 1px solid rgba(224, 224, 224, 1);
}

.functions-button {
  padding: 8px;
  background: rgba(245, 245, 245, 1);
  border-radius: 100px;
  margin-right: 10px;
  cursor: pointer;
}

.functions-button img {
  width: 14px;
  height: 14px;
  object-fit: contain;
}

.functions-button span {

  cursor: pointer;
  font-size: 11px;
}

.functions-button span img {
  width: 16px;
  /* icon x nhỏ hơn */
  height: 16px;

  opacity: 0.7;
  transition: opacity 0.2s;
}

.functions-button span img:hover {
  opacity: 1;
}

@media screen and (min-width:1200px) {
  .functions-button {
    padding: 2px;
    background: rgba(245, 245, 245, 1);
    border-radius: 100px;
    margin-right: 10px;
    cursor: pointer;
  }
}

.tools {
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
  transition: background 0.2s ease, opacity 0.2s ease;

  img {
    width: 16px;
    height: 16px;
  }

  .tool-item {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
  }

  .submenu-arrow {
    margin-left: auto;
    font-size: 18px;
    line-height: 1;
    color: #6e6b7b;
  }

  .tools-submenu {
    position: absolute;
    top: 0;
    left: calc(100% + 8px);
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(15, 23, 42, 0.12);
    border: 1px solid rgba(15, 23, 42, 0.08);
    padding: 6px 0;
    min-width: 140px;
    z-index: 20;

    p {
      margin: 0;
      padding: 8px 16px;
      cursor: pointer;
      white-space: nowrap;

      &.disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      &:hover:not(.disabled) {
        background: rgba(245, 245, 245, 1);
      }
    }
  }
}

.tools:hover {
  background: rgba(245, 245, 245, 1);
  cursor: pointer;
}

.tools.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

::-webkit-scrollbar {
  width: 10px;
}

.document-chatbot {
  background: rgb(255, 255, 255);
  transition: all 0.2s ease;
  border-radius: 6px;
}

.document-chatbot:hover {
  background: rgba(230, 230, 230, 1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.container-document-chatbot {
  max-height: 300px;
  overflow-y: auto;
  overflow-x: hidden;
}

/* Chỉ khi màn hình nhỏ hơn 768px */
@media screen and (max-width: 768px) {
  .container-document-chatbot {
    position: absolute;
    left: 0;
    bottom: 0;
  }
}

.icon-line {
  display: flex;
  align-items: flex-start;
  background: #f5f5f5;
  padding: 10px;
  border-radius: 10px;
  font-family: sans-serif;
  font-size: 14px;
  max-width: 600px;
}

.icon-line .icons {
  margin-right: 8px;
  display: flex;
  flex-direction: column;
  line-height: 1;
  cursor: pointer;
}

.content-text {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.5em;
  max-height: 4.5em;
}

.popover-box {
  position: absolute;
  background-color: white;
  border: 1px solid #ccc;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  z-index: 999;
}

.function-item {
  padding: 10px 25px;
}

.function-item:hover {
  background-color: #f0f0f0;
}

.message-card {
  display: flex;
  align-items: flex-start;
  padding: 0.25rem;
}

.bubble {
  background: #f6f7f9;
  border-radius: 16px;
  padding: 16px 20px;
  max-width: 720px;
  box-shadow: 0 1px 0 rgba(16, 24, 40, 0.02);
}

.bubble-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
}

.icon {
  width: 18px;
  height: 18px;
  flex: 0 0 18px;
  opacity: 0.7;
  margin-top: 2px;
}

.selection-text {
  margin: 0;
  line-height: 1.4;
  color: #8b8f98;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.collapse-btn {
  background: transparent;
  border: none;
  cursor: pointer;
}

.file-list {
  margin: 0;
  line-height: 1.4;
  color: #353535;
  display: flex;
  flex-wrap: wrap;
  max-height: none;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.file-list.collapsed {
  display: none;
}

.file-item {
  display: inline-block;
  border: 1px solid #353535;
  padding: 4px 10px;
  border-radius: 14px;
  margin: 0 2px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  max-width: 150px;
  cursor: pointer;
}

.chatbot-container-relative {
  position: absolute;
  top: 60px;
  left: 50%;
  transform: translateX(-50%);
  height: 100%;
  width: 90%;
}

.chatbot-main-flex-size {
  min-width: 0;
  min-height: 0;
  position: relative;
}

// .chatbot-main-flex-size::before {
//   content: "";
//   position: absolute;
//   bottom: -80px;
//   left: -400px;
//   width: calc(100% + 400px);
//   height: 100px;
//   background-color: #fff;
//   z-index: 0;
//   /* nằm dưới nội dung thật */
// }

/* ========== CANVAS FULL HEIGHT ========== */
.canvas-pane {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

.canvas-body {
  flex: 1 1 auto;
  overflow-y: auto !important;
  min-height: 0;
}

/* Kéo dài vùng xem nội dung document */
:host ::ng-deep .view-detail-file-content-container {
  height: 100% !important;
  min-height: 0;
}
.chatbot-main-flex-size>* {
  position: relative;
  z-index: 1;
  /* để nội dung không bị lớp trắng che */
}

.chatbot-top-right-button {
  position: absolute;
  top: 10px;
  right: 10px;
}

.chatbot-sidebar {
  background-color: rgba(249, 249, 249, 1);
  color: rgba(33, 37, 41, 1);
}

.chatbot-group-label {
  font-size: 12px;
  font-weight: 700;
}

.chatbot-icon-padding-5 {
  padding: 5px;
}

.chatbot-empty-title {
  font-size: 20px;
  font-weight: 700;
}

.chatbot-quota-banner {
  background: #fff;
  width: 100%;
  display: flex;
}


.chatbot-inline-block {
  display: inline-block;
}

.z-9999 {
  // position:relative  !important;
  z-index: 999 !important;

}

/* ============================================
   RESPONSIVE STYLES - Mobile & Tablet
   ============================================ */

/* Responsive cho màn hình width <= 768px */
@media screen and (max-width: 768px) {

  /* Container adjustments */
  /* CSS gốc cho iOS (hoặc mặc định) */
  body.is-android .container-chatbot {
    height: 95vh !important;
  }

  body.is-ios .container-chatbot {
    height: 100vh !important;
    max-height: 100vh !important;
    overflow: hidden;
  }



  .custom-dialog.maximized {
    padding: 0 !important;
  }

  /* Hiển thị nút menu mobile */
  .mobile-menu-toggle {
    display: block;
  }

  /* Ẩn sidebar mặc định trên mobile */
  .chatbot-sidebar {
    position: fixed !important;
    left: -100% !important;
    top: 0;
    height: 100vh;
    width: 280px !important;
    max-width: 80vw;
    z-index: 1000;
    background: rgba(249, 249, 249, 1);
    transition: left 0.3s ease;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
  }

  /* Hiển thị sidebar khi mở */
  .chatbot-sidebar.mobile-sidebar-open {
    left: 0 !important;
  }

  /* Hiển thị overlay khi sidebar mở */
  .chatbot-sidebar.mobile-sidebar-open~.mobile-sidebar-overlay,
  .mobile-sidebar-overlay {
    display: block;
  }

  /* Hiển thị nút đóng trên mobile */
  .mobile-sidebar-close {
    display: block;
  }

  /* Điều chỉnh main chat area */
  .chatbot-main-flex-size {
    width: 100% !important;
    padding: 0 !important;
  }

  /* Điều chỉnh chat content */
  .chat-content {
    flex: none !important;
    flex-grow: 0 !important;
    flex-shrink: 0 !important;
    height: calc(100vh - 360px) !important;
    max-height: calc(100vh - 360px) !important;
    max-width: 90% !important;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    padding: 8px !important;
  }

  .chat-content-maximized {
    width: 100% !important;
    max-width: 100% !important;
    padding: 0.5rem !important;
    margin: 0 !important;
  }

  /* Message bubbles */
  .message-bubble {
    padding: 0.5rem !important;
  }

  .answer {
    max-width: 100%;
    padding: 10px 14px !important;
    font-size: 14px;
  }

  .answer.user {
    padding: 10px 14px !important;
  }

  /* Chat input container */
  .chat-input-container {
    padding: 8px !important;
    background: white;
    max-width: 100% !important;
    width: 100% !important;

  }

  .input-container {
    max-width: 100%;
    padding: 6px;
  }

  .chat-input {
    font-size: 14px;
    padding: 8px !important;
    max-height: 120px;
  }

  /* Tool buttons */
  .tool-chatbot {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .tool {
    flex-wrap: wrap;
    gap: 0.25rem;
  }

  .icon-button {
    padding: 0.4rem;
    font-size: 12px;
  }

  /* Ẩn label công cụ trên màn hình nhỏ */
  .tool-label {
    display: none;
  }

  /* Buttons */
  .chat-send-button,
  .chat-cancel-button {
    width: 44px !important;
    height: 44px !important;
    min-width: 44px;
    min-height: 44px;
  }

  /* Top buttons positioning */
  .chatbot-top-right-button {
    top: 15px !important;
    right: 15px !important;
    z-index: 998;
  }

  /* Scroll button */
  .scroll-to-bottom {
    bottom: 160px !important;
    right: 15px !important;
    width: 40px;
    height: 40px;
  }

  /* Empty state */
  .chatbot-empty-title {
    font-size: 18px;
  }

  .centered-input {
    padding-bottom: 30px;
  }

  /* Quota banner */
  .chatbot-quota-banner {
    font-size: 12px;
    padding: 0.25rem;
  }

  /* Function buttons */
  .functions-button {
    font-size: 12px;
    padding: 6px 10px;
    margin-right: 6px;
  }

  .function-chat {
    margin-bottom: 8px !important;
  }

  /* Context menu */
  .context-menu {
    max-width: 200px;
    font-size: 13px;
  }

  /* File list */
  .file-list-wrapper {
    max-width: 100%;
  }

  .file-item {
    font-size: 12px;
    padding: 6px 10px;
    max-width: 120px;
  }

  /* Accordion/Collapse */
  .collapse-icon {
    font-size: 13px;
  }

  /* Thinking section */
  .thinking {
    font-size: 13px;
  }

  /* Badge pills for tools */
  .badge-pill {
    font-size: 11px;
    padding: 0.35rem 0.65rem;
  }

  /* Search input in sidebar */
  .chatbot-sidebar .form-control {
    font-size: 14px;
  }

  /* Conversation items */
  .coversation {
    font-size: 13px;
    padding: 0.5rem !important;
  }

  .coversation-contain {
    padding: 6px;
  }

  /* New chat button */
  .button-add-conversation-minimized {
    display: none;
  }

  .new-chat {
    padding: 0.75rem !important;
  }

  .new-chat .h4 {
    font-size: 16px;
  }

  /* Icon line */
  .icon-line {
    max-width: 100%;
    font-size: 13px;
    padding: 8px;
  }

  /* Bubble */
  .bubble {
    max-width: 100%;
    padding: 12px 16px;
  }

  /* Tool chatbot adjustments */
  .chatbot-export-zoom {
    zoom: 1;
  }
}

@media screen and (min-width:768.01px) and (max-width: 1200px) {

  /* Container adjustments */
  /* CSS gốc cho iOS (hoặc mặc định) */
  body.is-android .container-chatbot {
    height: 85vh !important;
  }

  body.is-ios .container-chatbot {
    height: 100vh !important;
    max-height: 100vh !important;
    overflow: hidden;
  }
}

/* Portrait tablet (768x1024) - điều chỉnh thêm cho màn dọc */
// @media screen and (max-width: 768px) and (orientation: portrait) {
//   .chat-content {
//     height: calc(100vh - 300px) !important;
//     max-height: calc(100vh - 300px) !important;
//   }

//   .chat-input-container {
//     max-height: 220px;
//   }

//   .centered-input {
//     height: 85%;
//     padding-bottom: 40px;
//   }
// }

// /* Landscape mobile (chiều ngang) */
// @media screen and (max-width: 768px) and (orientation: landscape) {
//   .chat-content {
//     height: calc(100vh - 240px) !important;
//     max-height: calc(100vh - 240px) !important;
//   }

//   .chatbot-sidebar {
//     width: 240px !important;
//   }

//   .chat-input-container {
//     max-height: 160px;
//   }
// }

.chat-content .answer table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  margin-bottom: 16px;

  & th,
  & td {
    border: 1px solid #e0e0e0;
    padding: 8px 12px;
    text-align: left;
  }
}

.selection-popover {
  position: absolute;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 6px 10px;
  min-height: 40px;
  background: #fff;
  border: 1px solid rgba(2, 18, 43, .12);
  border-radius: 12px;
  box-shadow: 0 12px 24px rgba(2, 18, 43, .12);
  z-index: 2147483000;
}

.selection-popover .btn-note {
  border: 0;
  background: transparent;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 6px;
  font-size: 14px;
  line-height: 1;
  color: #0f172a;
  cursor: pointer;
  border-radius: 10px;
}

.selection-popover .btn-note:hover {
  background: #f6f7fb;
}

/* icon */
.selection-popover .btn-note img {
  width: 18px;
  height: 18px;
  margin-right: 6px;
  vertical-align: -3px;
  display: none !important;
  opacity: .9;
}

.chat-content {
  position: relative;
}

.selection-popover .btn-note {
  border: 0;
  background: transparent;
  cursor: pointer;
}
:host ::ng-deep .doc-viewer-sheet .modal-dialog {
  margin: 0;
  position: fixed;
  right: 0; top: 0;
  height: 100vh;
  max-width: 60vw;
  width: 900px;
  transform: none !important;
}

:host ::ng-deep .doc-viewer-sheet .modal-content {
  height: 100vh;
  border-radius: 0;
  display: flex;
  flex-direction: column;
}

:host ::ng-deep .doc-viewer-sheet .modal-body {
  overflow: auto;    
}

:host ::ng-deep .modal-backdrop.show { opacity: 0.2; }

/* wrapper chiếm full cao khi fullscreen */
.chat-split-wrapper {
  height: calc(100vh - 80px);
  display: flex;
  background: #f7f9fc;
}

/* trái: chat */
.chat-pane {
  min-width: 320px;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #e6eef6;

  .chat-scroll {
    flex: 1 1 auto;
    overflow: auto;
    padding: 12px 16px;
    background: #fff;
  }
}

/* thanh kéo */
.pane-resizer {
  width: 6px;
  cursor: col-resize;
  background: transparent;
  position: relative;

  &:after {
    content: "";
    position: absolute; inset: 0;
    background: rgba(0,0,0,.05);
  }
}

/* phải: canvas */
.canvas-pane {
  min-width: 320px;
  max-width: 70%;
  display: flex;
  flex-direction: column;
  background: #fff;

  .canvas-toolbar {
    display: flex; align-items: center; justify-content: space-between;
    gap: 8px;
    padding: 10px 12px;
    border-bottom: 1px solid #eef2f7;
    .title { font-weight: 600; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; }
  }

  .canvas-body {
    flex: 1 1 auto;
    overflow: auto;
    animation: fadeIn .25s ease-out;
  }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(4px); }
  to   { opacity: 1; transform: none; }
}
.chatbot-file-card {
  display: flex;
  align-items: center;
  width: 100%;
  max-width: 420px;
  padding: 10px 20px;
  border-radius: 12px;  
  background: #f5f7ff; 
  border: 1px solid #edf1ff;
  cursor: pointer;
  transition: border-color 0.15s ease, box-shadow 0.15s ease,
    background 0.15s ease, transform 0.1s ease;
}

.chatbot-file-card:hover {
  border-color: #2f80ed;
  box-shadow: 0 0 0 1px rgba(47, 128, 237, 0.18);
  background: #ffffff;
}

.chatbot-file-card__icon {
  flex: 0 0 auto;
  width: 40px;
  height: 40px;
  border-radius: 999px;
  background: #e7f0ff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;

  img {
    width: 22px;
    height: 22px;
  }
}

.chatbot-file-card__body {
  flex: 1 1 auto;
  min-width: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.chatbot-file-card__title {
  font-size: 13px;
  font-weight: 600; 
  color: #111827;
  margin-bottom: 2px;
}

.chatbot-file-card__meta {
  display: inline-flex;
  align-items: center;
  font-size: 11px;
  color: #6b7280;
}

.chatbot-file-card__meta-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-right: 6px;

  i {
    width: 12px;
    height: 12px;
  }
}

.chatbot-file-card__meta-text {
  white-space: nowrap;
}


/* Container fullscreen: hiển thị ngang */
.container-chatbot.maximized {
  display: flex;
  flex-direction: row;
  height: 100%;
}
.canvas-pane {
  display: flex;
  flex-direction: column;
  height: 100vh !important;
  overflow: hidden !important;   // khóa không cho panel tự cuộn
}
.canvas-pane ::ng-deep .view-detail-file-content-container {
  height: calc(100vh - 120px) !important;   // trừ header & tab
  overflow-y: auto !important;
  overflow-x: hidden;
}
.canvas-pane ::ng-deep .view-detail-file-content-wrapper {
  max-height: 100% !important;
  overflow-y: auto !important;
}
:host {
  overflow: hidden !important;
}
.container-chatbot.maximized {
  height: 100vh !important;
  overflow: hidden !important;
}
.canvas-body {
  flex: 1;
  overflow-y: auto !important;
}
html, body {
  height: 100%;
  overflow: hidden !important;
}
/* Sidebar đã có col-3 rồi, bổ sung cho chắc */
.chatbot-sidebar {
  flex: 0 0 auto;
  min-width: 220px;
}

/* Vùng chat chính (bên trái canvas) */
.chatbot-main-flex-size {
  flex: 1 1 auto;
  min-width: 0;
  display: flex;
  flex-direction: column;
}

/* Vùng nội dung chat scroll được */
.chat-content {
  flex: 1 1 auto;
  min-height: 0;
  overflow-y: auto;
}

/* ==== Thanh kéo giữa ==== */
.pane-resizer {
  flex: 0 0 auto;
  width: 6px;
  cursor: col-resize;
  display: none; /* ẩn mặc định */
}

.container-chatbot.maximized .pane-resizer {
  display: block;
  position: relative;
}

.pane-resizer::before {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  left: 50%;
  width: 2px;
  transform: translateX(-50%);
  background-color: #e5e7eb;
}

/* ==== Panel bên phải (canvas giống Claude) ==== */
.canvas-pane {
  flex: 0 0 auto;
  display: flex;
  flex-direction: column;
  min-width: 0;
  border-left: 1px solid #e5e7eb;
  background-color: #fafafa;
}

/* header của panel phải */
.canvas-toolbar {
  padding: 6px 10px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.canvas-toolbar .title {
  font-weight: 600;
  font-size: 14px;
}

/* thân panel phải */
.canvas-body {
  flex: 1 1 auto;
  min-height: 0;
  padding: 0;
  overflow-y: auto;
}

.canvas-pane .canvas-body app-view-detail-file {
  display: block;
  margin-top: 30px;
}

/* Mobile: không chia đôi nữa, anh có thể chỉnh thêm nếu muốn */
@media (max-width: 992px) {
  .pane-resizer,
  .canvas-pane {
    display: none !important;
  }
}
.file-item {
  border-radius: 6px;
  padding: 4px 8px;
  transition: background-color .15s ease, color .15s ease, box-shadow .15s ease;
}

.file-item.active {
  background: #e0f2fe;        // nền xanh nhạt
  color: #0f172a;
  box-shadow: 0 0 0 1px #0284c7 inset;
}

.chatbot-file-card {
  transition: border-color .15s ease, background-color .15s ease,
              box-shadow .15s ease, transform .1s ease;
}

.chatbot-file-card.active {
  background: #eff6ff;
  border-color: #2563eb;
  box-shadow: 0 0 0 1px #2563eb;
  transform: translateY(-1px);
}

.pane-resizer {
  position: relative;     
  width: 6px;
  cursor: col-resize;
  background-color: transparent;
}

.chatbot-minimize-on-resizer {
  position: absolute;
  top: 14px; 
  right: 100%;
  margin-right: 6px;
  width: 22px;
  height: 22px;
  z-index: 9999;
}

.canvas-body ::ng-deep .view-detail-file-content-container {
  height: 100%;
}

.canvas-body ::ng-deep .view-detail-file-doc-viewer {
  height: calc(100vh - 150px);
}

.canvas-body {
  min-height: 0 !important;
  overflow: hidden !important;
}

.canvas-body app-view-detail-file {
  height: 100% !important;
  max-height: 100% !important;
  overflow: hidden !important;
}
