{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"Csign": {"root": "", "sourceRoot": "src", "projectType": "application", "prefix": "app", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"allowedCommonJsDependencies": ["<PERSON><PERSON><PERSON>", "node-waves", "app/main/ui/icons/feather/feather.component", "@angularclass/hmr", "ngx-quill", "lodash", "uuid", "<PERSON>ui<PERSON><PERSON><PERSON>", "dragula", "apexcharts", "chart.js", "contra/emitter", "crossvent", "highlight.js", "rxjs-compat", "node-forge", "rxjs/add/operator/do", "xlsx", "rxjs/add/operator/catch", "rxjs/add/observable/throw", "pdfjs-dist", "ngx-extended-pdf-viewer"], "baseHref": "/", "outputPath": "dist/cls", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets", "src/firebase-messaging-sw.js", "src/manifest.json", {"glob": "**/*", "input": "../node_modules/ngx-monaco-editor/assets/monaco", "output": "./assets/monaco/"}, {"glob": "**/*", "input": "node_modules/ngx-extended-pdf-viewer/assets/", "output": "/assets/"}], "styles": ["./node_modules/@angular/material/prebuilt-themes/indigo-pink.css", "src/styles.scss", "node_modules/flatpickr/dist/flatpickr.min.css", "node_modules/quill/dist/quill.snow.css", "node_modules/highlight.js/scss/atom-one-dark.scss", "src/assets/fonts/feather/iconfont.css", "src/assets/fonts/font-awesome/css/font-awesome.min.css", "src/assets/fonts/flag-icon-css/css/flag-icon.min.css", "src/@core/scss/angular/libs/toastr.component.scss", "node_modules/primeng/resources/themes/saga-blue/theme.css", "node_modules/primeng/resources/primeng.min.css", "node_modules/primeicons/primeicons.css", "node_modules/primeflex/primeflex.css"], "stylePreprocessorOptions": {"includePaths": ["node_modules", "src/assets/"]}, "scripts": ["./node_modules/katex/dist/katex.min.js", "./node_modules/quill/dist/quill.min.js", "./node_modules/mammoth/mammoth.browser.min.js"], "vendorChunk": true, "extractLicenses": false, "buildOptimizer": false, "sourceMap": true, "optimization": false, "namedChunks": true}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "baseHref": "/", "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "4mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "100kb"}]}, "develop": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.dev.ts"}], "baseHref": "/", "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "4mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "100kb"}]}, "devui": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.devui.ts"}], "baseHref": "/", "optimization": {"scripts": false, "styles": {"minify": true, "inlineCritical": false}, "fonts": true}, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "18", "maximumError": "20mb"}, {"type": "anyComponentStyle", "maximumWarning": "100kb"}]}, "prodv2": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prodv2.ts"}], "baseHref": "/", "optimization": {"scripts": false, "styles": {"minify": true, "inlineCritical": false}, "fonts": true}, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "18", "maximumError": "20mb"}, {"type": "anyComponentStyle", "maximumWarning": "100kb"}]}, "hmr": {"budgets": [{"type": "anyComponentStyle", "maximumWarning": "100kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.hmr.ts"}]}}, "defaultConfiguration": ""}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "Csign:build"}, "configurations": {"production": {"browserTarget": "Csign:build:production"}, "hmr": {"hmr": true, "browserTarget": "Csign:build:hmr"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "Csign:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.spec.json", "karmaConfig": "src/karma.conf.js", "styles": ["./node_modules/@angular/material/prebuilt-themes/indigo-pink.css", "src/styles.scss", "@core/scss/angular/libs/toastr.component.scss", "node_modules/flatpickr/dist/flatpickr.min.css"], "scripts": ["node_modules/apexcharts/dist/apexcharts.min.js"], "assets": ["src/favicon.ico", "src/assets"]}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["src/tsconfig.app.json", "src/tsconfig.spec.json"], "exclude": ["**/node_modules/**"]}}}}, "Csign-e2e": {"root": "e2e/", "projectType": "application", "prefix": "", "architect": {"e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "Csign:serve"}, "configurations": {"production": {"devServerTarget": "Csign:serve:production"}}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": "e2e/tsconfig.e2e.json", "exclude": ["**/node_modules/**"]}}}}}, "defaultProject": "Csign", "cli": {"analytics": false}}