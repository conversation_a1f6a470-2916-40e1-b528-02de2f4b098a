FROM node:14.21.3-bullseye as build

WORKDIR /usr/local/app
RUN npm i -g @angular/cli@14.0.6

COPY ["package.json", "package-lock.json*", "./"]

RUN npm install

COPY ./ /usr/local/app/

RUN ng build --configuration production --output-path=dist/cls --base-href /cls/ --deploy-url /cls/

FROM nginx:latest

COPY /nginx.conf /etc/nginx/conf.d/default.conf
COPY --from=build /usr/local/app/dist/ /app/
RUN rm /app//index.html
RUN cp /app//cls/index.html /app//index.html
EXPOSE 80