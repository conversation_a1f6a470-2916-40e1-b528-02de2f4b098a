<div class="auth-wrapper auth-v2 login-page login-bg">
  <div
    class="auth-inner row m-0 justify-content-center align-items-center position-relative"
  >
    <!-- Brand logo-->
    <a class="brand-logo" (click)="goHome()">
      <img
        #logoImg
        [src]="getLogoImage()"
        alt="brand-logo"
        height="28"
        (error)="logoImg.src = defaultLogo"
      />
      <h2 class="text-primary-theme brand-text text-white ml-1">
        {{ getAppName() }}
      </h2>
    </a>
    <!-- /Brand logo-->
    <!-- Left Text-->
    <!-- <div class="d-none d-lg-flex col-lg-8 align-items-center p-5">
      <div
        class="w-100 d-lg-flex align-items-center justify-content-center px-5"
      >
        <img
          class="img-fluid"
          [src]="
            coreConfig.layout.skin === 'dark'
              ? 'assets/images/pages/login-v2-dark.svg'
              : 'assets/images/pages/login-v2.svg'
          "
          alt="Login V2"
        />
      </div>
    </div> -->
    <!-- /Left Text-->
    <!-- Login-->
    <div class="auth-bg p-3 width-600px border-radius-12px">
      <div class="w-100">
        <h2 class="card-title font-weight-bold mb-1 gradient-text-primary-theme text-center">
          Hệ thống kiểm tra, rà soát VBQPPL Việt Nam
        </h2>
        <p class="card-text mb-2 text-center">
          Đăng nhập và bắt đầu trải nghiệm trợ lý ảo rà soát văn bản pháp luật
        </p>
      </div>
      
      <div class="mx-auto width-400px w-100">
        <ngb-alert [type]="'danger'" [dismissible]="false" *ngIf="error" class="d-flex justify-content-center">
          <div class="alert-body text-center w-100">{{ error }}</div>
        </ngb-alert>

        <ul ngbNav #navFilled="ngbNav" class="nav-tabs nav-fill" [activeId]="activeTab" (activeIdChange)="onTabChange($event)">
          <li ngbNavItem="login">
            <a class="text-primary-theme" ngbNavLink>Đăng nhập</a>
            <ng-template ngbNavContent>
              <form
                class="auth-login-form mt-2"
                [formGroup]="loginForm"
                (ngSubmit)="onSubmit($event)"
              >
                <div class="form-group">
                  <label class="form-label" for="login-email">Email</label>
                  <input
                    type="text"
                    formControlName="email"
                    class="form-control"
                    [ngClass]="{ 'is-invalid': submitted && f.email.errors }"
                    placeholder="Nhập email"
                    aria-describedby="login-email"
                    autofocus=""
                    tabindex="1"
                  />
                  <div
                    *ngIf="submitted && f.email.errors"
                    class="invalid-feedback"
                  >
                    <div *ngIf="f.email.errors.required">
                      Chưa nhập địa chỉ email
                    </div>
                    <div *ngIf="f.email.errors.email">
                      Địa chỉ email cần hợp lệ
                    </div>
                  </div>
                </div>
                <div class="form-group">
                  <div class="d-flex justify-content-between">
                    <label for="login-password">Mật khẩu</label>
                    <a class="text-primary-theme" routerLink="/pages/authentication/forgot-password-v2">
                      <small>Quên mật khẩu?</small>
                    </a>
                  </div>
                  <div
                    class="input-group input-group-merge form-password-toggle"
                  >
                    <input
                      [type]="passwordTextType ? 'text' : 'password'"
                      formControlName="password"
                      class="form-control form-control-merge"
                      [ngClass]="{
                        'is-invalid error': submitted && f.password.errors
                      }"
                      placeholder="Nhập mật khẩu"
                      aria-describedby="login-password"
                      tabindex="2"
                    />
                    <div class="input-group-append">
                      <span class="input-group-text cursor-pointer"
                        ><i
                          class="feather font-small-4"
                          [ngClass]="{
                            'icon-eye-off': passwordTextType,
                            'icon-eye': !passwordTextType
                          }"
                          (click)="togglePasswordTextType()"
                        ></i
                      ></span>
                    </div>
                  </div>
                  <div
                    *ngIf="submitted && f.password.errors"
                    class="invalid-feedback"
                    [ngClass]="{ 'd-block': submitted && f.password.errors }"
                  >
                    <div *ngIf="f.password.errors.required">
                      Chưa nhập mật khẩu
                    </div>
                  </div>
                </div>
                <!-- <div class="form-group">
                  <div class="custom-control custom-checkbox">
                    <input
                      class="custom-control-input"
                      id="remember-me"
                      type="checkbox"
                      tabindex="3"
                    />
                    <label class="custom-control-label" for="remember-me">
                      Ghi nhớ đăng nhập
                    </label>
                  </div>
                </div> -->
                <button
                  [disabled]="loading"
                  class="btn btn-primary-theme btn-block"
                  tabindex="4"
                  rippleEffect
                >
                  <span
                    *ngIf="loading"
                    class="spinner-border spinner-border-sm mr-1"
                  ></span>
                  Đăng nhập
                </button>
                <!-- <div class="divider">
                <div class="divider-text">Hoặc</div>
              </div>
              <div id="google-signin-button" class="d-flex justify-content-center"></div> -->
              </form>
            </ng-template>
          </li>
          <li ngbNavItem *ngIf="!isVPQHBrand">
            <a ngbNavLink>Đăng ký</a>
            <ng-template ngbNavContent>
              <app-auth-register-v2
                [socialUser]="user"
                (register)="onTabChange('login')"
              ></app-auth-register-v2>
            </ng-template>
          </li>
        </ul>
        <div [ngbNavOutlet]="navFilled" class="mt-2"></div>
      </div>
    </div>
    <!-- /Login-->
  </div>
    <ng-container *ngIf="isVPQHBrand; else defaultFooter">
    <div
      class="position-absolute right-10px bottom-0px bg-white border-radius-8px d-flex vpqh-footer-spacing"
      style="padding: 8px"
    >
      <img
        src="assets/images/pages/login/hdsd-qr.png"
        alt="QR hướng dẫn"
        width="120"
        height="120"
      />
    </div>
  </ng-container>
  <ng-template #defaultFooter>
    <p class="text-white position-absolute right-10px bottom-0px">
      Powered by CMC
    </p>
  </ng-template>
  <div
    *ngIf="isVPQHBrand"
    class="position-absolute left-10px bottom-0px d-flex flex-wrap align-items-center vpqh-footer-spacing"
  >
    <span>Tài liệu hướng dẫn:</span>
    <a
      [href]="guideDocumentUrl"
      target="_blank"
      rel="noopener"
      class="text-primary-theme ml-1"
      ><u>Hướng dẫn sử dụng</u></a
    >
    <span class="ml-1">Hotline hỗ trợ:</span>
    <span [href]="'tel:' + hotline" class="text-danger ml-1">{{ hotline }}</span>
  </div>
</div>
