<!-- Register-->
<div class="mb-1">
  <div class="progress height-20px">
    <div
      class="progress-bar"
      role="progressbar"
      [ngStyle]="{ width: (wizardStep/4)*100 + '%' }"
      [attr.aria-valuenow]="wizardStep"
      aria-valuemin="1"
      aria-valuemax="4"
    >
      Bước {{ wizardStep }}/4
    </div>
  </div>
</div>

<div class="d-flex align-items-center">
  <form
    class="auth-register-form w-100"
    [formGroup]="registerForm"
    autocomplete="off"
    autocorrect="off"
    spellcheck="false"
  >
    <div *ngIf="wizardStep === 1">
      <!-- Email Field (always visible, always first) -->
      <ng-container>
        <div class="form-group">
          <label for="register-username" class="form-label">
            Tên tài khoản
          </label>
          <input
            type="text"
            formControlName="username"
            class="form-control"
            placeholder="Nhập tên tài k<PERSON>"
            aria-describedby="register-username"
            tabindex="1"
            autocomplete="off"
            autocapitalize="off"
            spellcheck="false"
            maxlength="255"
            [ngClass]="{ 'is-invalid': f.username.errors && (f.username.touched || submitted) }"
          />
          <div
            *ngIf="f.username.errors && (f.username.touched || submitted)"
            class="invalid-feedback"
          >
            <div *ngIf="f.username.errors.required">
              Bạn cần nhập tên tài khoản
            </div>
            <div *ngIf="f.username.errors.maxlength">
              Tên tài khoản tối đa 255 ký tự
            </div>
          </div>

          <div class="form-group my-1">
            <label for="register-email" class="form-label">
              Email
            </label>
            <div class="input-group">
              <input
                type="email"
                formControlName="email"
                class="form-control"
                placeholder="Nhập Email"
                aria-describedby="register-email"
                tabindex="2"
                autocomplete="off"
                autocapitalize="off"
                spellcheck="false"
                [ngClass]="{
                  'is-invalid': (submitted || f.email.touched) && f.email.errors,
                  'is-valid': is_email_valid
                }"
                [readonly]="is_email_valid"
              />
              <!-- Nút kiểm tra email khi chưa valid, nút sửa khi đã valid -->
              <div class="input-group-append">
                <button
                  *ngIf="!is_email_valid"
                  type="button"
                  class="btn btn-outline-primary text-nowrap"
                  (click)="checkEmail()"
                  [disabled]="!f.email.value || f.email.errors"
                >
                  Kiểm tra email
                </button>
                <button
                  *ngIf="is_email_valid"
                  type="button"
                  class="btn btn-outline-secondary text-nowrap"
                  [disabled]="socialUser"
                  (click)="is_email_valid=false"
                >
                  Sửa email
                </button>
              </div>
            </div>
            <div
              *ngIf="(submitted || f.email.touched) && f.email.errors"
              class="text-danger mt-25"
            >
              <small *ngIf="f.email.errors.required">
                Bạn cần nhập địa chỉ email
              </small>
              <small *ngIf="f.email.errors.email">Địa chỉ email không hợp lệ</small>
              <small *ngIf="f.email.errors.emailExists">Email đã tồn tại</small>
            </div>
            <!-- <div *ngIf="is_email_valid" class="valid-feedback d-block">
              Email hợp lệ, bạn có thể tiếp tục đăng ký.
            </div> -->
          </div>
        </div>

        <!-- <div class="form-group">
          <label for="register-password" class="form-label">Mật khẩu</label>
          <div class="input-group input-group-merge form-password-toggle">
            <input
              [type]="passwordTextType ? 'text' : 'password'"
              formControlName="password"
              class="form-control form-control-merge"
              placeholder="············"
              aria-describedby="register-password"
              tabindex="3"
              autocomplete="new-password"
              maxlength="255"
              autocapitalize="off"
              spellcheck="false"
              [ngClass]="{
                'is-invalid error':
                  (submitted || f.password.touched) && f.password.errors
              }"
            />
            <div class="input-group-append">
              <span class="input-group-text cursor-pointer">
                <i
                  class="feather font-small-4"
                  [ngClass]="{
                    'icon-eye-off': passwordTextType,
                    'icon-eye': !passwordTextType
                  }"
                  (click)="togglePasswordTextType()"
                ></i>
              </span>
            </div>
          </div>
          <div
            *ngIf="(submitted || f.password.touched) && f.password.errors"
            class="invalid-feedback"
            [ngClass]="{
              'd-block':
                (submitted || f.password.touched) && f.password.errors
            }"
          >
            <div *ngIf="f.password.errors.required">
              Bạn cần nhập mật khẩu
            </div>
            <div *ngIf="f.password.errors.minlength">
              Mật khẩu phải chứa ít nhất {{ f.password.errors.minlength.requiredLength }} ký tự
            </div>
            <div *ngIf="f.password.errors.pattern">
              Mật khẩu phải chứa ít nhất 1 ký tự in hoa và 1 ký tự đặc biệt
            </div>
            <div *ngIf="f.password.errors.maxlength">
              Mật khẩu không được vượt quá {{ f.password.errors.maxlength.requiredLength }} ký tự
            </div>
          </div>
        </div>
        <div class="form-group">
          <label for="register-password" class="form-label">Xác nhận mật khẩu</label>
          <input
            [type]="'password'"
            formControlName="confPassword"
            class="form-control form-control-merge"
            placeholder="············"
            aria-describedby="register-password"
            tabindex="4"
            autocomplete="new-password"
            autocapitalize="off"
            spellcheck="false"
            [ngClass]="{
              'is-invalid error':
                (submitted || f.confPassword.dirty) &&
                f.confPassword.errors,
              'is-valid':
                !f.confPassword.errors?.mustMatch && f.confPassword.dirty
            }"
          />
          <div
            *ngIf="
              (submitted || f.confPassword.dirty) && f.confPassword.errors
            "
            class="invalid-feedback"
            [ngClass]="{
              'd-block':
                (submitted || f.confPassword.dirty) && f.confPassword.errors
            }"
          >
            <div *ngIf="f.confPassword.errors.required">
              Mật khẩu không được trống
            </div>
            <div *ngIf="f.confPassword.errors.mustMatch">
              Mật khẩu không khớp
            </div>
          </div>
          <div *ngIf="f.confPassword.errors == null" class="valid-feedback">
            Xác nhận mật khẩu đúng
          </div>
        </div> -->
        
        <div class="form-group">
          <div class="custom-control custom-checkbox">
            <input
              class="custom-control-input"
              type="checkbox"
              id="register-privacy-policy"
              tabindex="3"
              formControlName="agree"
              autocomplete="off"
              [ngClass]="{
                'is-invalid': f.agree.errors && (f.agree.touched || submitted)
              }"
            />
            <label class="custom-control-label" for="register-privacy-policy">
              Tôi đồng ý với
              <a href="javascript:void(0);">Chính sách bảo mật và điều khoản</a>
            </label>
            <div
              *ngIf="f.agree.errors && (f.agree.touched || submitted)"
              class="invalid-feedback"
            >
              <div *ngIf="f.agree.errors.required">
                Bạn cần đồng ý với Chính sách bảo mật và điều khoản
              </div>
            </div>
          </div>
        </div>

        <div class="d-flex justify-content-center mt-1">
          <button type="button" class="btn btn-primary" [disabled]="!registerForm.valid || !is_email_valid" (click)="wizardStep=2" rippleEffect>Tiếp theo</button>
        </div>
      </ng-container>
    </div>

    <div *ngIf="wizardStep === 2">
      <h4 class="text-center">Giúp chúng tôi hiểu hơn về nhu cầu của bạn</h4>

      <div class="row mt-1">
        <div class="col-md-6 d-flex justify-content-center">
          <div
            class="card w-100 mb-0 text-center p-1 user-type-card cursor-pointer d-flex flex-column justify-content-start align-items-center"
            [ngClass]="{'selected': registerForm.get('user_type')?.value === 'individual'}"
            (click)="registerForm.get('user_type')?.setValue('individual')"
          >
            <div class="height-30px width-30px">
              <span data-feather="user" class="icon h-100 w-100"></span>
            </div>
            <h5 class="mt-2 mb-1">Người dùng cá nhân</h5>
            <div class="text-left mt-2 mb-0 w-100">
              <div class="d-flex align-items-start mb-1 desc">
                <div class="d-flex justify-content-center flex-shrink-0 width-24px">
                  <span data-feather="check"></span>
                </div>
                <div class="flex-grow-1 pl-8px">
                  Tìm kiếm (Giới hạn tiêu chí)
                </div>
              </div>
              <div class="d-flex align-items-start mb-1 desc">
                <div class="d-flex justify-content-center flex-shrink-0 width-24px">
                  <span data-feather="check"></span>
                </div>
                <div class="flex-grow-1 pl-8px">
                  Chuyển đổi (Giới hạn số lượt)
                </div>
              </div>
              <div class="d-flex align-items-start mb-1 desc">
                <div class="d-flex justify-content-center flex-shrink-0 width-24px">
                  <span data-feather="check"></span>
                </div>
                <div class="flex-grow-1 pl-8px">
                  Rà soát (Giới hạn số lượt)
                </div>
              </div>
              <div class="d-flex align-items-start mb-1 desc">
                <div class="d-flex justify-content-center flex-shrink-0 width-24px">
                  <span data-feather="check"></span>
                </div>
                <div class="flex-grow-1 pl-8px">
                  Chatbot (Giới hạn số lượt)
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="col-md-6 d-flex justify-content-center">
          <div
            class="card w-100 mb-0 text-center p-1 card user-type-card cursor-pointer d-flex flex-column justify-content-start align-items-center"
            [ngClass]="{'selected': registerForm.get('user_type')?.value === 'organization'}"
            (click)="registerForm.get('user_type')?.setValue('organization')"
          >
            <div class="height-30px width-30px">
              <span data-feather="briefcase" class="icon h-100 w-100"></span>
            </div>
            <h5 class="mt-2 mb-1">Tổ chức/Doanh nghiệp</h5>
            <div class="text-left mt-2 mb-0 w-100">
              <div class="d-flex align-items-start mb-1 desc">
                <div class="d-flex justify-content-center flex-shrink-0 width-24px">
                  <span data-feather="check"></span>
                </div>
                <div class="flex-grow-1 pl-8px">
                  Tìm kiếm không giới hạn
                </div>
              </div>
              <div class="d-flex align-items-start mb-1 desc">
                <div class="d-flex justify-content-center flex-shrink-0 width-24px">
                  <span data-feather="check"></span>
                </div>
                <div class="flex-grow-1 pl-8px">
                  Chuyển đổi định dạng (Không giới hạn số lượt)
                </div>
              </div>
              <div class="d-flex align-items-start mb-1 desc">
                <div class="d-flex justify-content-center flex-shrink-0 width-24px">
                  <span data-feather="check"></span>
                </div>
                <div class="flex-grow-1 pl-8px">
                  Chatbot (Không giới hạn số lượt)
                </div>
              </div>
              <div class="d-flex align-items-start mb-1 desc">
                <div class="d-flex justify-content-center flex-shrink-0 width-24px">
                  <span data-feather="check"></span>
                </div>
                <div class="flex-grow-1 pl-8px">
                  Rà soát (Không giới hạn số lượt)
                </div>
              </div>
              <div class="d-flex align-items-start mb-1 desc">
                <div class="d-flex justify-content-center flex-shrink-0 width-24px">
                  <span data-feather="check"></span>
                </div>
                <div class="flex-grow-1 pl-8px">
                  Quản lý tổ chức
                </div>
              </div>
              <div class="d-flex align-items-start mb-1 desc">
                <div class="d-flex justify-content-center flex-shrink-0 width-24px">
                  <span data-feather="check"></span>
                </div>
                <div class="flex-grow-1 pl-8px">
                  Quản lý người dùng
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="mt-1" *ngIf="registerForm.get('user_type')?.value === 'organization'">
        <label for="register-organization-name" class="form-label">Tên tổ chức</label>
        <input
          type="text"
          id="register-organization-name"
          class="form-control"
          formControlName="organization_name"
          placeholder="Nhập tên tổ chức"
          maxlength="225"
        />
      </div>

      <div class="d-flex justify-content-between">
        <button type="button" class="btn btn-secondary mt-2 mr-2" (click)="wizardStep=1" rippleEffect>Quay lại</button>
        <button type="button" class="btn btn-primary mt-2" (click)="wizardStep=3" rippleEffect>Tiếp tục</button>
      </div>
    </div>
  
    <div *ngIf="wizardStep === 3">
      <h4 class="text-center">Giúp chúng tôi hiểu hơn về nhu cầu của bạn</h4>
      
      <div class="mt-1">
        <label for="register-industry" class="form-label">Bạn đang làm trong lĩnh vực nào</label>
        <ng-select
          id="register-industry"
          [items]="[
            { label: 'Công nghệ thông tin', value: 'Công nghệ thông tin' },
            { label: 'Giáo dục', value: 'Giáo dục' },
            { label: 'Tài chính & Ngân hàng', value: 'Tài chính & Ngân hàng' },
            { label: 'Y tế', value: 'Y tế' },
            { label: 'Logistic', value: 'Logistic' },
            { label: 'Luật', value: 'Luật' },
            { label: 'Marketing', value: 'Marketing' },
            { label: 'Sản xuất', value: 'Sản xuất' },
            { label: 'Khác', value: 'Khác' }
          ]"
          bindLabel="label"
          bindValue="value"
          formControlName="industry"
          placeholder="Chọn lĩnh vực"
          [clearable]="true"
        ></ng-select>
      </div>

      <div class="mt-1">
        <label for="register-scale" class="form-label">Quy mô tổ chức</label>
        <ng-select
          id="register-scale"
          [items]="[
            { label: 'Dưới 10 người', value: 'Dưới 10 người' },
            { label: '10-50 người', value: '10-50 người' },
            { label: 'Trên 50 người', value: 'Trên 50 người' }
          ]"
          bindLabel="label"
          bindValue="value"
          formControlName="scale"
          placeholder="Chọn quy mô tổ chức"
          [clearable]="true"
        ></ng-select>
      </div>
      
      <div class="d-flex justify-content-between">
        <button type="button" class="btn btn-secondary mt-2 mr-2" (click)="wizardStep=2" rippleEffect>Quay lại</button>
        <button type="button" class="btn btn-primary mt-2" (click)="wizardStep=4" rippleEffect>Tiếp tục</button>
      </div>
    </div>

    <div *ngIf="wizardStep === 4">

      <div class="mt-1">
        <label for="register-source" class="form-label">Bạn biết đến CLS qua đâu?</label>
        <ng-select
          id="register-source"
          [items]="[
            { label: 'Hội thảo/Workshop', value: 'Hội thảo/Workshop' },
            { label: 'Bạn bè/Đồng nghiệp giới thiệu', value: 'Bạn bè/Đồng nghiệp giới thiệu' },
            { label: 'Cơ quan/Đơn vị giới thiệu', value: 'Cơ quan/Đơn vị giới thiệu' },
            { label: 'Mạng xã hội', value: 'Mạng xã hội' },
            { label: 'Báo chí/Trang tin', value: 'Báo chí/Trang tin' },
            { label: 'Khác', value: 'Khác' }
          ]"
          bindLabel="label"
          bindValue="value"
          formControlName="source"
          placeholder="Chọn nguồn"
          [clearable]="true"
        ></ng-select>
      </div>

      <div class="mt-1">
        <label for="register-product-familiarity" class="form-label">
          Bạn đã từng sử dụng sản phẩm tương tự như vậy bao giờ chưa?
        </label>
        <ng-select
          id="register-product-familiarity"
          [items]="[
            { label: 'Chưa bao giờ', value: 'low' },
            { label: 'Thỉnh thoảng', value: 'medium' },
            { label: 'Thường xuyên', value: 'high' }
          ]"
          bindLabel="label"
          bindValue="value"
          formControlName="product_familiarity"
          placeholder="Chọn mức độ"
          [clearable]="true"
        ></ng-select>
      </div>

      <div class="mt-1">
        <label for="register-description" class="form-label">Điều gì khiến bạn quan tâm?</label>
        <textarea
          id="register-description"
          class="form-control"
          placeholder="Nhập lý do, mong đợi hoặc mối quan tâm của bạn"
          formControlName="description"
          rows="4"
        ></textarea>
      </div>

      <div class="d-flex justify-content-between">
        <button type="button" class="btn btn-secondary mt-2 mr-2" (click)="wizardStep=3" rippleEffect>Quay lại</button>
        <button type="button" class="btn btn-primary mt-2" (click)="onSubmit()" rippleEffect>Đăng ký</button>
      </div>
    </div>
  </form>
</div>
<!-- /Register-->
