import { Component, Input, OnInit, TemplateRef, ViewChild, ViewEncapsulation } from "@angular/core";
import { CoreConfigService } from "@core/services/config.service";
import {
  getLogoImage as getLogoImageHelper,
  getApp<PERSON><PERSON> as getAppNameHelper,
} from "app/shared/image.helper";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { Router } from "@angular/router";
import { NgbActiveModal, NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { AuthenticationService } from "app/auth/service";
import { FormType } from "app/models/FormType";
import { ToastrService } from "ngx-toastr";
import { ThemeService } from "@core/services/theme.service";
import { OrganizationService } from "../organization.service";
import { emailValidator } from "app/shared/EmailValidator";

@Component({
  selector: "app-organization-control",
  templateUrl: "./organization-control.component.html",
  styleUrls: ["./organization-control.component.scss"],
  // encapsulation: ViewEncapsulation.None,
})
export class OrganizationControlComponent implements OnInit {
  @Input("modal") public modal: NgbActiveModal;
  @Input("title") public title: string;
  @Input("type") public type: FormType;
  @Input("data") public data: any;
  @Input("listOrganization") public listOrganization: any;

  @ViewChild("modalSelectOrg") modalSelectOrg: TemplateRef<any>;

  public breadcrumbDefault: object;
  public organizationForm: FormGroup;
  public FormType = FormType;
  public statusOrgan = [
    { value: "active", label: "Hoạt động" },
    { value: "inactive", label: "Không hoạt động" },
  ];
  public submitted: boolean = false;
  public role: string;

  logoPreviewUrl: string | ArrayBuffer | null = null;
  backgroundPreviewUrl: string | ArrayBuffer | null = null;
  // Track whether current preview equals system default (Create mode intent)
  isDefaultLogo: boolean = false;
  isDefaultBackground: boolean = false;
  isDefaultColor: boolean = true;

  private static readonly DEFAULT_COLOR = '#008fe3';
  // Preserve user's own app_name so we can restore it after clearing parent
  private ownAppNameBeforeInheritance: string | null | undefined;

  // Derived flag to know when a parent org is selected
  get hasSelectedParent(): boolean {
    return !!this.organizationForm?.get("parent_organization")?.value;
  }

  public tempSelectedOrganization: any;
  public selectedOrganization: any;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private _organService: OrganizationService,
    private _authenService: AuthenticationService,
    private _toast: ToastrService,
    private _coreConfigService: CoreConfigService,
    private _themeService: ThemeService,
    private modalService: NgbModal,
  ) {
    this.organizationForm = this.fb.group({
      name: [null, [Validators.required, Validators.maxLength(255)]],
      description: [null, Validators.maxLength(255)],
      represent_people: [null, Validators.maxLength(255)],
      email: [null, [Validators.maxLength(255), emailValidator]],
      phone: [
        null,
        [Validators.pattern(/^0\d{9}$/), Validators.maxLength(255)],
      ],
      address: [null, Validators.maxLength(255)],
      app_name: [null, [Validators.required, Validators.maxLength(50)]],
      parent_organization: [null],
      logo_image: [null],
      background_image: [null],
      color_code: ['#008fe3'],
    });
    this.role = _authenService.currentUserValue.role;
    if (this.role == "ADMIN")
      this.organizationForm.patchValue({
        parent_organization: localStorage.getItem("organization_id"),
      });
  }

  buildFlatTreeForNgSelect(data: any[]): any[] {
    // Bước 1: Tạo map id → node để gom các node theo id
    const nodeMap: { [id: string]: any } = {};
    data.forEach((org) => {
      nodeMap[org.id] = { ...org, children: [] };
    });

    // Bước 2: Gắn các node vào đúng cha của nó trong key children
    const roots: any[] = [];
    data.forEach((org) => {
      if (org.parent_organization && nodeMap[org.parent_organization]) {
        nodeMap[org.parent_organization].children.push(nodeMap[org.id]);
      } else {
        roots.push(nodeMap[org.id]);
      }
    });

    return roots;
  }

  onSelectOrganizationClick(event: MouseEvent) {
    event.stopPropagation();
    event.preventDefault();
    this.openModal(this.modalSelectOrg);
  }

  openModal(modal) {
    this.modalService.open(modal, {
      scrollable: true
    });
  }

  setTempOrganization(node: any): void {
    this.tempSelectedOrganization = node;
  }

  isTempSelected(node: any): boolean {
    if (!this.tempSelectedOrganization) return false;
    return this.tempSelectedOrganization.id === node.id;
  }

  shouldShowNode(node: any, search: string): boolean {
    if (!search) return true;
    const nodeName = (node.name || '').toLowerCase();
    const searchValue = search.toLowerCase();
    if (nodeName.includes(searchValue)) return true;
    if (Array.isArray(node.children)) {
      return node.children.some(child => this.shouldShowNode(child, search));
    }
    return false;
  }

  confirmOrganization(modal: any, node?: any): void {
    if (node) {
      this.tempSelectedOrganization = node;
    }
    if (!this.tempSelectedOrganization) return;
    this.selectedOrganization = this.tempSelectedOrganization;
    this.organizationForm.patchValue({ parent_organization: this.selectedOrganization.id });
    modal.close();
  }

  ngOnInit(): void {
    if (this.listOrganization) {
      this.listOrganization = this.buildFlatTreeForNgSelect(this.listOrganization)
    }

    if (this.type == FormType.Update) {
      this.organizationForm.patchValue({ ...this.data });

      this.logoPreviewUrl = this.data?.logo_image || null;
      this.backgroundPreviewUrl = this.data?.background_image || null;

      const parentId = this.organizationForm.get('parent_organization')?.value;
      if (parentId) {
        const matchNode = (this.listOrganization || []).find((org: any) => org && org.id === parentId);
        this.selectedOrganization = matchNode ?? null;
        this.tempSelectedOrganization = matchNode ?? null;
      }
    }

    // React to parent organization selection changes
    this.organizationForm
      .get("parent_organization")
      ?.valueChanges.subscribe(() => this.applyImagesFromSelectedParent());

    // Apply once on init in case value already present
    this.applyImagesFromSelectedParent();

    // If creating and no parent selected, initialize with default images as File
    if (
      this.type === FormType.Create &&
      !this.organizationForm.get("parent_organization")?.value
    ) {
      this.restoreDefaultImage('assets/images/logo/CMC.png', 'logo');
      this.restoreDefaultImage('assets/images/illustration/loginBG.png', 'background');
      this.restoreDefaultColor();
    }
  }
  get f() {
    return this.organizationForm.controls;
  }
  submitSaveOrganization() {
    this.submitted = true;
    if (this.organizationForm.invalid) {
      return;
    }
    const formData = new FormData();
    const v = this.organizationForm.value;
    [
      "name",
      "description",
      "represent_people",
      "email",
      "phone",
      "address",
      "app_name",
      "parent_organization",
      "color_code",
    ].forEach((k) => {
      let val = v[k];
      if (val == null && val == undefined) {
        val = '';
      }
      formData.append(k, val);
    });

    // files: only if user picked new ones
    if (v.logo_image instanceof File) {
      formData.append("logo_image", v.logo_image, v.logo_image.name);
    }
    if (v.background_image instanceof File) {
      formData.append(
        "background_image",
        v.background_image,
        v.background_image.name
      );
    }

    if (this.type == FormType.Create) {
      this._organService.addOrganization(formData).subscribe(
        (res) => {
          this.modal.close();
          this._organService.reloadData.next(true);
          this._toast.success(
            this.role == "SUPER_ADMIN" ? "Thêm tổ chức" : "Thêm phòng ban",
            "Thành công",
            {
              closeButton: true,
              positionClass: "toast-top-right",
              toastClass: "toast ngx-toastr",
            }
          );
        },
        (error) => {
          this._toast.error(
            this.role == "SUPER_ADMIN"
              ? "Tổ chức đã tồn tại"
              : "Phòng ban đã tồn tại",
            "Thất bại",
            {
              closeButton: true,
              positionClass: "toast-top-right",
              toastClass: "toast ngx-toastr",
            }
          );
        }
      );
    } else {
      this._organService.updateOrganization(formData, this.data.id).subscribe(
        (res) => {
          this.modal.close();
          this._organService.reloadData.next(true);

          // Check if the updated organization matches the current user's organization
          const currentOrganizationId = localStorage.getItem("organization_id");
          if (currentOrganizationId && currentOrganizationId === this.data.id) {
            // Update localStorage with new logo and background image URLs
            if (res.logo_image) {
              localStorage.setItem("logo_image", res.logo_image);
            }
            if (res.background_image) {
              localStorage.setItem("background_image", res.background_image);
            }
            if (res.color_code) {
              localStorage.setItem("color_code", res.color_code);
              this._themeService.setTheme(res.color_code);
            }
            if (res.app_name) {
              localStorage.setItem("app_name", res.app_name);
            }

            // Refresh runtime logo in core config immediately after organization update
            this._coreConfigService.setConfig(
              {
                app: {
                  appLogoImage: getLogoImageHelper(),
                  appName: getAppNameHelper(),
                  appTitle: getAppNameHelper(),
                },
              },
              { emitEvent: true }
            );
          }

          this._toast.success(
            this.role == "SUPER_ADMIN"
              ? "Cập nhật tổ chức"
              : "Cập nhật phòng ban",
            "Thành công",
            {
              closeButton: true,
              positionClass: "toast-top-right",
              toastClass: "toast ngx-toastr",
            }
          );
        },
        (error) => {
          this._toast.error(
            this.role == "SUPER_ADMIN"
              ? "Tổ chức đã tồn tại"
              : "Phòng ban đã tồn tại",
            "Thất bại",
            {
              closeButton: true,
              positionClass: "toast-top-right",
              toastClass: "toast ngx-toastr",
            }
          );
        }
      );
    }
  }

  onCancel() {
    this.router.navigate(["super-admin/organization"]);
  }

  onSelectImage(event: Event, type: "logo" | "background"): void {
    const file = (event.target as HTMLInputElement).files?.[0];
    if (!file) return;

    // Validate file type
    const allowedTypes = [
      "image/jpeg",
      "image/jpg",
      "image/png",
      "image/gif",
      "image/webp",
    ];
    if (!allowedTypes.includes(file.type)) {
      this._toast.error(
        "Vui lòng chọn file hình ảnh hợp lệ (JPG, PNG, GIF, WebP)",
        "Lỗi",
        {
          closeButton: true,
          positionClass: "toast-top-right",
          toastClass: "toast ngx-toastr",
        }
      );

      // Clear the file input
      (event.target as HTMLInputElement).value = "";
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      const result = e.target?.result as string;

      if (type === "logo") {
        this.logoPreviewUrl = result;
        this.organizationForm.get("logo_image")?.setValue(file);
        this.isDefaultLogo = false;
      } else {
        this.backgroundPreviewUrl = result;
        this.organizationForm.get("background_image")?.setValue(file);
        this.isDefaultBackground = false;
      }
    };
    reader.readAsDataURL(file);
  }

  restoreDefaultImage(image: string, type: "logo" | "background") {
    // Fetch the default logo image
    fetch(image)
      .then((response) => response.blob())
      .then((blob) => {
        // Convert blob to File object
        const file = new File([blob], "CMC.png", { type: blob.type || "image/png" });
        
        // Set the file in the form control (so it passes instanceof File check)
        if (type === "logo") {
          this.organizationForm.get("logo_image")?.setValue(file);
          this.isDefaultLogo = true;
        } else {
          this.organizationForm.get("background_image")?.setValue(file);
          this.isDefaultBackground = true;
        }
        
        // Update preview URL using FileReader
        const reader = new FileReader();
        reader.onload = (e) => {
          if (type === "logo") {
            this.logoPreviewUrl = e.target?.result as string;
          } else {
            this.backgroundPreviewUrl = e.target?.result as string;
          }
        };
        reader.readAsDataURL(file);
      })
      .catch((error) => {
        // console.error("Error loading default logo:", error);
        this._toast.error(
          "Không thể tải hình ảnh mặc định",
          "Lỗi",
          {
            closeButton: true,
            positionClass: "toast-top-right",
            toastClass: "toast ngx-toastr",
          }
        );
      });
  }

  // restoreDefaultBackground() {}

  updateColor(event: Event) {
    const input = event.target as HTMLInputElement;
    this.organizationForm.patchValue({ color_code: input.value });
    this.isDefaultColor = input.value === OrganizationControlComponent.DEFAULT_COLOR;
  }

  onHexInputChange(event: Event) {
    const input = (event.target as HTMLInputElement).value.trim();

    // Simple HEX validation: must start with '#' and have 6 hex digits
    const isValidHex = /^#([0-9A-Fa-f]{6})$/.test(input);

    if (isValidHex) {
      this.organizationForm.patchValue({ color_code: input });
    }
  }

  private applyImagesFromSelectedParent(): void {
    const parentId = this.organizationForm.get("parent_organization")?.value;
    if (parentId) {
      // Capture current app_name once before inheriting from parent
      if (this.ownAppNameBeforeInheritance === undefined) {
        const current = this.organizationForm.get("app_name")?.value;
        this.ownAppNameBeforeInheritance = current ?? null;
      }
      const selected = (this.listOrganization || []).find(
        (o: any) => o && o.id === parentId
      );
      this.logoPreviewUrl = selected?.logo_image || null;
      this.backgroundPreviewUrl = selected?.background_image || null;
      this.isDefaultLogo = false;
      this.isDefaultBackground = false;
      this.isDefaultColor = false;
      // Clear any previously picked files so request won't upload them
      this.organizationForm.get("logo_image")?.reset(null);
      this.organizationForm.get("background_image")?.reset(null);
      // Hydrate app_name from selected parent organization if available
      // Set parent's app_name; if null/empty, blank the field explicitly
      if (selected) {
        const parentAppName = selected.app_name ?? null;
        this.organizationForm.patchValue({ app_name: parentAppName });
      }
    } else {
      // Revert to original data (for Update) or default images (for Create)
      if (this.type == FormType.Update) {
        this.logoPreviewUrl = this.data?.logo_image || null;
        this.backgroundPreviewUrl = this.data?.background_image || null;
        this.isDefaultLogo = false;
        this.isDefaultBackground = false;
        this.isDefaultColor = this.organizationForm.get('color_code')?.value === OrganizationControlComponent.DEFAULT_COLOR;
        const restored =
          this.ownAppNameBeforeInheritance !== undefined
            ? this.ownAppNameBeforeInheritance
            : this.data?.app_name ?? null;
        this.organizationForm.patchValue({ app_name: restored });
      } else {
        // Use system default images and set them as File values so they are submitted
        this.restoreDefaultImage('assets/images/logo/CMC.png', 'logo');
        this.restoreDefaultImage('assets/images/illustration/loginBG.png', 'background');
        this.restoreDefaultColor();
  
        const restored =
          this.ownAppNameBeforeInheritance !== undefined
            ? this.ownAppNameBeforeInheritance
            : null;
        this.organizationForm.patchValue({ app_name: restored });
      }
      // Reset capture so future selections re-capture the user's current value
      this.ownAppNameBeforeInheritance = undefined;
    }
  }

  restoreDefaultColor() {
    this.organizationForm.patchValue({ color_code: OrganizationControlComponent.DEFAULT_COLOR });
    this.isDefaultColor = true;
  }
}
