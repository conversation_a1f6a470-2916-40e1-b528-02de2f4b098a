import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Params } from "@angular/router";
import { InterceptorSkipHeader } from "@core/components/loading/loading.interceptor";
import { environment } from "environments/environment";
import { Observable } from "rxjs";

@Injectable({
  providedIn: "root",
})
export class BocTachThongTinService {
  constructor(private _httpClient: HttpClient) { }
  getAllDocumentInWorkSpace(params) {
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");

    return this._httpClient.get<any>(`${environment.apiUrl}/ocr/documents`, {
      params: params,
      headers: headers,
    });
  }
  getAllFileOcr(page) {
    const params: Params = {
      page: page,
    };
    return this._httpClient.get<any>(`${environment.apiUrl}/ocr/documents`, {
      params,
    });
  }
  reloadFile(idFile) {
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");

    const body = {};
    return this._httpClient.post<any>(
      `${environment.apiUrl}/ocr/documents/${idFile}/rerun`,
      body,
      { headers }
    );
  }
  deleteDocument(id) {
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    const body = {};
    return this._httpClient.delete<any>(
      `${environment.apiUrl}/ocr/documents/${id}/delete`,
      {
        headers: headers,
        body: body
      }
    );
  }
  addFile(body) {
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    return this._httpClient.post<any>(
      `${environment.apiUrl}/ocr/documents`,
      body,
      { headers }
    );
  }
  updateDocumentName(documentId: number, newName: string): Observable<any> {
    return this._httpClient.put(
      `${environment.apiUrl}/document/${documentId}/update-name/`,
      { name: newName }
    );
  }
  saveFile(file, fileType: string): Observable<boolean> {
    return new Observable((observer) => {
      const nameParts = file.name.split(".");
      nameParts.pop();
      const fileName = nameParts.join(".");

      this.downloadFile(file.id, fileType).subscribe(
        (blob) => {
          try {
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement("a");
            a.href = url;
            a.download = `${fileName}.${fileType}`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);

            observer.next(true);
            observer.complete();
          } catch (e) {
            observer.error(e);
          }
        },
        (error) => {
          observer.error(error);
        }
      );
    });
  }

  saveFileSearch(file, fileType: string): Observable<boolean> {
    return new Observable((observer) => {
      this.downloadFileFromSearch(file.id, fileType).subscribe(
        async (res: any) => {
          if (!res.url) {
            observer.error("NO_URL");
            return;
          }

          try {
            const response = await fetch(res.url);
            if (!response.ok) {
              observer.error("FETCH_FAILED");
              return;
            }

            const blob = await response.blob();
            const blobUrl = window.URL.createObjectURL(blob);

            const a = document.createElement("a");
            a.href = blobUrl;
            a.download = `${file.name}.${fileType}`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);

            window.URL.revokeObjectURL(blobUrl);

            observer.next(true);
            observer.complete();
          } catch (err) {
            observer.error(err);
          }
        },
        (error) => {
          observer.error(error);
        }
      );
    });
  }


  private downloadFile(docId: number, fileType: string): Observable<Blob> {
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    return this._httpClient.get(
      `${environment.apiUrl}/document/download?doc_id=${docId}&type=${fileType}`,
      {
        responseType: "blob", // Để nhận file nhị phân
        headers: headers,
      }
    );
  }
  private downloadFileFromSearch(docId: string, fileType: string): Observable<any> {
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    return this._httpClient.get(
      `${environment.apiUrl}/document/download-from-cls-doc?doc_id=${docId}&type=${fileType}`,
      { headers: headers }
    );
  }


}
