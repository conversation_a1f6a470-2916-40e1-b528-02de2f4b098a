default:
  interruptible: true
  
before_script:
  - docker login -u $REGISTRY_IMAGE_USER -p $REGISTRY_IMAGE_TOKEN $CI_REGISTRY

workflow:
  rules:

    - if: $CI_COMMIT_BRANCH == "mainquochoi"
      variables:
        REGISTRY_IMAGE: registry.gitlab.com/ailabcati/prod/cls-web-frontend
        DOCKERFILE: Dockerfile

    - if: $CI_COMMIT_BRANCH == "mainbtp"
      variables:
        REGISTRY_IMAGE: registry.gitlab.com/ailabcati/prod/clsbtp-web-frontend
        DOCKERFILE: Dockerfile

    - if: $CI_COMMIT_BRANCH == "develop"
      variables:
        REGISTRY_IMAGE: registry.gitlab.com/ailabcati/dev/clsv3-web-frontend
        DOCKERFILE: Dockerfile_dev
    - if: $CI_COMMIT_BRANCH == "main"
      variables:
        REGISTRY_IMAGE: registry.gitlab.com/ailabcati/prod/clsv3-web-frontend
        DOCKERFILE: Dockerfile_prod


stages:
  - build
  - deploy
  - scan


build:
  stage: build
  script:
    - docker pull $REGISTRY_IMAGE:latest || true
    - docker build --cache-from $REGISTRY_IMAGE:latest --tag $REGISTRY_IMAGE:$CI_COMMIT_SHA --tag $REGISTRY_IMAGE:latest -f $DOCKERFILE .
    - docker push $REGISTRY_IMAGE:$CI_COMMIT_SHA
    - docker push $REGISTRY_IMAGE:latest
  only:
    - main
    - develop
    - mainbtp
    - mainquochoi

deploy:
  stage: deploy
  # image: alpine:3.11
  image: cmcatilab/alpine:3.11_git_kustomize
  before_script:
    # - apk add --no-cache git curl bash
    # - curl -s "https://raw.githubusercontent.com/kubernetes-sigs/kustomize/master/hack/install_kustomize.sh"  | bash
    # - mv kustomize /usr/local/bin/
    - git remote set-url origin https://$ARGOCD_REGISTRY_USER:$ARGOCD_REGISTRY_TOKEN@$ARGOCD_REPO
    - git clone https://$ARGOCD_REGISTRY_USER:$ARGOCD_REGISTRY_TOKEN@$ARGOCD_REPO
    - cd argo-config
  script:
    - git checkout -B main
    - cd $CONFIG_DIR
    - kustomize edit set image $REGISTRY_IMAGE:$CI_COMMIT_SHA
    - git config --global user.email "<EMAIL>"
    - git config --global user.name "Gitlab Runner"
    - git commit -am "CLS FE - $CI_COMMIT_BRANCH - $CI_COMMIT_MESSAGE"
    - git push origin main
  only:
    - main
    - develop
    - mainbtp
    - mainquochoi


sonar:
  stage: scan
  image:
    name: sonarsource/sonar-scanner-cli:11
    entrypoint: [""]
  before_script: [] 
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"
    GIT_DEPTH: "0"
  cache:
    policy: pull-push
    key: "sonar-cache-$CI_COMMIT_REF_SLUG"
    paths:
      - "${SONAR_USER_HOME}/cache"
      - sonar-scanner/
  script:
    - |
      echo "Bắt đầu quét SonarQube..."
      sonar-scanner -Dsonar.host.url="${SONAR_HOST_URL}"
      if [ $? -eq 0 ]; then
        SONAR_LINK="${SONAR_HOST_URL}/dashboard?id=${CI_PROJECT_NAME}"
        curl -H "Content-Type: application/json" \
            -X POST \
            -d "{
              \"embeds\": [{
                \"title\": \"SonarQube Scan Completed\",
                \"description\": \"Dự án **${CI_PROJECT_NAME}** đã được quét thành công!\",
                \"url\": \"${SONAR_LINK}\",
                \"color\": 3066993,
                \"fields\": [
                  {\"name\": \"🕒 Branch\", \"value\": \"${CI_COMMIT_REF_NAME}\"},
                  {\"name\": \"👤 Committer\", \"value\": \"${GITLAB_USER_NAME}\"},
                  {\"name\": \"📊 Xem báo cáo tại\", \"value\": \"[Mở SonarQube](http://*********:9001/projects)\"}
                ]
              }]
            }" \
            "$WEBHOOK_URL"
      fi
  allow_failure: true
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "master"
    - if: $CI_COMMIT_BRANCH == "main"
    - if: $CI_COMMIT_BRANCH == "develop"