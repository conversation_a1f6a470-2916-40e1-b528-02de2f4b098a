import {
  Compo<PERSON>,
  <PERSON>ementRef,
  HostListener,
  Input,
  OnInit,
  QueryList,
  Renderer2,
  ViewChild,
  ViewChildren,
  ViewEncapsulation,
} from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { NgbActiveModal, NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { ViewDetailFileService } from "app/layout/components/view-detail-file/view-detail-file.service";
import { FormType } from "app/models/FormType";
import { ShowContent } from "app/models/ShowContent";
import { TypeDocument } from "app/models/TypeDocument";
import { environment } from "environments/environment";
import { ToastrService } from "ngx-toastr";
import { Subject } from "rxjs";
import { filter, switchMap, takeUntil, tap } from "rxjs/operators";
import Swal from "sweetalert2";
import { randomUuidv4 } from "../../../../../../../util/randomUUID";
import { DetailWorkSpaceService } from "../detail-work-space.service";
import { ListDocumentService } from "../list-document/list-document.service";
import { ChatbotService } from "./chatbot.service";
import { TakeNoteService } from "../take-note/take-note.service";
import { NgbPopover } from '@ng-bootstrap/ng-bootstrap';

import {
  Document as DocxDocument,
  Packer,
  Paragraph,
  HeadingLevel,
  TextRun,
  Table,
  TableRow,
  TableCell,
  WidthType,
  BorderStyle,
  AlignmentType,
  VerticalAlign,
} from "docx";

const DATA_SOURCE_OPTIONS = [
  {
    value: 1,
    label: "Kho VBQPPL",
    icon: "assets/images/icons/database.svg",
  },
  {
    value: 2,
    label: "Google",
    icon: "assets/images/icons/search.svg",
  },
];
@Component({
  selector: "app-chatbot",
  templateUrl: "./chatbot.component.html",
  styleUrls: ["./chatbot.component.scss"],
  encapsulation: ViewEncapsulation.None
})
export class ChatbotComponent implements OnInit {
  @ViewChild('chatContainer') private chatContainer!: ElementRef<HTMLDivElement>;
  @ViewChild("viewFileModal") public viewFileModal: any;
  @ViewChildren("message") messageElements: QueryList<ElementRef>;
  @ViewChild("lastMessage") lastMessage!: ElementRef;
  @ViewChildren("replyMessage") replyMessages!: QueryList<ElementRef>;
  @ViewChild("answerContainer") answerContainer!: ElementRef;
  @ViewChild('queryInput') queryInput!: ElementRef<HTMLTextAreaElement>;
  @ViewChild('toolsPop') toolsPop: NgbPopover;
  @Input("modal") public modal: NgbActiveModal;
  @Input("type") public type: FormType;
  @Input() disableQuickNote = false; 
  
  private lastMouse = { x: 0, y: 0 };
  @HostListener('document:mouseup', ['$event'])
  captureMouse(e: MouseEvent) {
    this.lastMouse = { x: e.clientX, y: e.clientY };
  }
  private _nodeIn(el: HTMLElement, node: Node): boolean {
    const target = node.nodeType === Node.TEXT_NODE ? (node.parentElement as Node) : node;
    return el.contains(target);
  }
  listConversation = [];
  groupedConversations: { label: string; items: any[] }[] = [];
  groupedConversationsFilter: { label: string; items: any[] }[] = [];
  public types: FormType;
  messages = [];
  userInput = "";
  public isMaximized: boolean = false;
  public thinkingText: string = "";
  public answerText: string = "";
  public idMessage: number = 0;
  public bodyMessage: {
    role: string;
    content: string;
    selection_text: string;
    selected_save_files: any[];
    selected_upload_files: any[];
  }[] = [];
  abortController: AbortController | null = null;
  public selectedConversation: any = null;
  public checkDoneAnswer: boolean = true;
  public unSubAll: Subject<any> = new Subject();
  public workspaceId: string = "";
  public quota: number = 0;
  contextMenuPosition = { x: 0, y: 0 };
  public contextMenuVisible = false;
  public contextMenuItem: any = null;
  public isEditChatbotName: boolean = false;
  public searchConversationString: string = "";
  public statusThinking: string = "";
  public showScrollButton = false;
  private autoScroll: boolean = true; // Track if auto-scroll is enabled
  private lastScrollTop: number = 0; // Track previous scroll position to detect scroll direction
  public listTool = [
    {
      value: "data-source",
      label: "Nguồn dữ liệu",
      icon: "assets/images/icons/databasee.svg",
      children: DATA_SOURCE_OPTIONS,
    },
    {
      value: 3,
      label: "Tải câu trả lời",
      icon: "assets/images/icons/file-export.svg",
    },
    {
      value: 4,
      label: "Chế độ sáng tạo",
      icon: "assets/images/icons/bulb.svg",
      badgeIcon: "assets/images/icons/bulb-blue.svg",
    },
    {
      value: 5,
      label: "Soạn thảo",
      icon: "assets/images/icons/pencil.svg",
      badgeIcon: "assets/images/icons/pencil-blue.svg",
    },
  ];
  public checkDoneThinking = false;
  public toolSelected = [
    {
      value: 1,
      label: "Kho VBQPPL",
      icon: "assets/images/icons/database.svg",
    },
  ];
  public textAskChatbot: string = null;
  public listDocument: any = [];
  public selectedDocuments: boolean[] = [];
  public selectedFile: any[] = []; // để lưu file được chọn
  public isHasTextFromVanBan: boolean = false;
  chatHeight: number = 0;
  private initialSpacerHeight: number = 0;
  private lastSpacerUpdate: number = 0;
  private spacerUpdatePending: boolean = false;
  private lastReplyHeight: number = 0;
  public selection_text: string = null;
  public selected_save_files = [];
  public selected_upload_files = [];
  public workSpaceName: string = "";
  public fileId: string;
  public es_id: string;
  public typeDocument: string;
  public isSpeechToText: boolean = false;
  public idMessageSpeechToText: string = null;
  isMobileSidebarOpen: boolean = false;
  private selectedDocumentKeys = new Set<string>();
  public isCreativeMode: boolean = false; // Chế độ sáng tạo
  public isDraftMode: boolean = false; 
  public activeSubmenu: string | number = null;
  private submenuCloseTimeout: any = null;
  splitView = false;
  rightWidthPct = 40;  
  private isResizing = false;
  private startX = 0;
  private startRightWidthPct = 40;
  canvasFile: any = null;
  private draftRawBuffer: string = "";
  private draftBlockContent: string | null = null;
  public activeFileKey: string | null = null;
  openCanvas(file: any) {
    this.canvasFile = file;
    this.splitView = true;
    if (!this.rightWidthPct) {
      this.rightWidthPct = 40;
    }
    this.markActiveFile(file);
  }

  closeCanvas() {
    this.splitView = false;
    this.canvasFile = null;
  }

  // bắt đầu kéo thanh chia
  startResize(event: MouseEvent) {
    event.preventDefault();
    this.isResizing = true;
    this.startX = event.clientX;
    this.startRightWidthPct = this.rightWidthPct;
  }

  @HostListener('document:mousemove', ['$event'])
  onMouseMove(event: MouseEvent) {
    if (!this.isResizing) return;

    const deltaX = event.clientX - this.startX;

    const container = (event.target as HTMLElement).closest('.container-chatbot') as HTMLElement;
    if (!container) return;

    const containerWidth = container.clientWidth;
    const deltaPct = (deltaX / containerWidth) * 100;

    let newRight = this.startRightWidthPct - deltaPct;

    newRight = Math.max(20, Math.min(70, newRight));
    this.rightWidthPct = newRight;
  }

  @HostListener('document:mouseup')
  stopResize() {
    this.isResizing = false;
  }

  private submenuOpenedByClick: boolean = false; // Track if submenu was opened by click (stays open) or hover (can timeout)
  constructor(
    private workSpaceService: DetailWorkSpaceService,
    private chatbotService: ChatbotService,
    private route: ActivatedRoute,
    private toast: ToastrService,
    private viewDetailFileService: ViewDetailFileService,
    private router: Router,
    private listDocumentService: ListDocumentService,
    private modalService: NgbModal,
    private renderer: Renderer2,
    private noteService: TakeNoteService,
  ) {}
  private markActiveFile(file: any) {
    const key = this.getDocumentKey(file);
    if (key) this.activeFileKey = key;
  }

  public isActiveFile(file: any): boolean {
    if (!file) return false;
    const key = this.getDocumentKey(file);
    return !!key && key === this.activeFileKey;
  }
    // ====== TEMPLATE NỘI DUNG QUYẾT ĐỊNH (có thể đổi theo nhu cầu) ======
  private _buildQuyetDinhHTML(payload: {
    tinh?: string; so?: string; ngay?: string; co_quan?: string; ten_de_an?: string; can_cu?: string[];
  }): { title: string; html: string } {
    const TINH = payload.tinh || 'THÀNH PHỐ HÀ NỘI';
    const SO = payload.so || '5261/QĐ-UBND';
    const NGAY = payload.ngay || new Date().toLocaleDateString('vi-VN');
    const CQ  = payload.co_quan || 'ỦY BAN NHÂN DÂN THÀNH PHỐ HÀ NỘI';
    const DEAN = payload.ten_de_an || 'đề án xây dựng và phát triển quan hệ lao động hài hòa, ổn định và tiến bộ';
    const CANCU = payload.can_cu?.length ? payload.can_cu : [
      'Luật Giáo dục số 43/2019/QH14;',
      'Nghị định số 37/2025/NĐ-CP ngày 26/02/2025 của Chính phủ;'
    ];

    const title = `Quyết định phê duyệt đề án của UBND ${TINH}`;
    const html = `
<div style="font-family:Times New Roman; font-size:14pt; line-height:1.4;">
  <div style="text-transform:uppercase; text-align:center; font-weight:bold;">
    ${CQ}<br/><span style="font-weight:normal">Số: ${SO}</span>
  </div>
  <div style="text-align:center; margin-top:6px;">
    <div style="text-transform:uppercase; font-weight:bold;">CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM</div>
    <div>Độc lập - Tự do - Hạnh phúc</div>
    <div style="margin-top:6px;"><i>Hà Nội, ${NGAY}</i></div>
  </div>
  <h3 style="text-align:center; margin:18px 0 8px; text-transform:uppercase;">QUYẾT ĐỊNH</h3>
  <p style="text-align:center;"><b>Về việc phê duyệt ${DEAN}</b></p>
  <p><b>${CQ}</b></p>
  <p><i>Căn cứ</i>:</p>
  <ul>${CANCU.map(c=>`<li>${c}</li>`).join('')}</ul>
  <p style="text-align:center; font-weight:bold; text-transform:uppercase;">QUYẾT ĐỊNH</p>
  <p><b>Điều 1.</b> Phạm vi áp dụng...</p>
  <p><b>Điều 2.</b> Mục tiêu...</p>
  <p><b>Điều 3.</b> Nội dung chính của đề án...</p>
  <p><b>Điều 4.</b> Trách nhiệm thực hiện...</p>
  <p><b>Điều 5.</b> Hiệu lực thi hành...</p>
  <p style="text-align:right; margin-top:20px;">TM. ỦY BAN NHÂN DÂN<br/><b>CHỦ TỊCH</b></p>
</div>`.trim();

    return { title, html };
  }

  private _maybeHandleLocalDrafting(raw: string): boolean {
    const useDraftTool = this.isDraftMode;
    const isDraftCmd =
      /soạn thảo/i.test(raw) &&
      /quyết định phê duyệt đề án/i.test(raw);

    if (!useDraftTool && !isDraftCmd) return false;

    // Ở đây anh có thể parse thêm từ raw nếu muốn tuỳ biến tiêu đề/nội dung
    const { title, html } = this._buildQuyetDinhHTML({
      tinh: "Thành phố Hà Nội",
      co_quan: "Ủy ban nhân dân Thành phố Hà Nội",
      so: "5261/QĐ-UBND",
    });

    const nowIso = new Date().toISOString();

    // Tạo "văn bản local" trong chatbotService (mock, không gọi API)
    const doc = this.chatbotService.createLocalDoc({
      name: title,
      label: "QĐ-UBND-HN",
      trich_yeu: title,
      toan_van: html,
      loai_van_ban: "Quyết định",
      so_hieu: "5261/QĐ-UBND",
      co_quan_ban_hanh: "UBND TP Hà Nội",
      tinh_trang_hieu_luc: "Chưa xác định",
    });

    const docWithTime = { ...doc, created_at: nowIso };

    // Đẩy 1 message assistant có đính kèm file vào list messages
    this.messages.push({
      id: new randomUuidv4().randomUuidv4(),
      role: "assistant",
      thinking: "",
      answer: `<answer>\n\nĐã tạo **${title}**.\n\n`,
      fileAttached: docWithTime,
    });

    this.checkDoneAnswer = true;
    setTimeout(() => this.scrollToBottom(), 10);

    return true;
  }

  // private openDocViewerLarge() {
  //   try { this.modalService?.dismissAll(); } catch {}
  //   this.modalOpen(this.viewFileModal, FormType.Maximized, 'xl');
  //   this.isMaximized = true;
  //   this.workSpaceService.isMaximized.next(true);
  // }

  @HostListener("document:keydown.escape", ["$event"])
  handleEscapeKey(event: KeyboardEvent) {
    if (this.type != FormType.Chatbot) this.isMaximized = false; //điều kiện này là tránh cho lúc xem văn bản từ chatbot ngoài không gian dự án
  }
  // private openDocById(fileId: string, fileName?: string) {
  //   if (!fileId) return;

  //   // 1) Đẩy fileId vào queryParams để middle pane bắt được
  //   this.router.navigate([], {
  //     queryParams: {
  //       fileId,
  //       tabs: 'toanvan',
  //       type: 'searching',
  //       time: new Date().getTime(),
  //       fileName: fileName ? this.transform(fileName) : null,
  //       save: true
  //     },
  //     queryParamsHandling: 'merge'
  //   });

  //   // 2) Cho layout hiểu đang mở văn bản ở chế độ ĐỌC
  //   this.listDocumentService.FileSearchTemp.next(null);
  //   this.listDocumentService.setBehavior(ShowContent.Search);
  // }
  private getSelectedHtmlIn(container: HTMLElement): string | null {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return null;

    const range = selection.getRangeAt(0);
    const common = range.commonAncestorContainer;
    if (!container.contains(common)) return null;

    const frag = range.cloneContents();
    const div = document.createElement("div");
    div.appendChild(frag);
    const html = div.innerHTML.trim();

    // loại rỗng (chỉ tag)
    const textOnly = html.replace(/<[^>]*>/g, "").trim();
    if (!textOnly) return null;

    // chặn ảnh vì addNote() đang không cho ảnh
    if (/<img\s/i.test(html)) return null;

    return html;
  }

  ngOnInit(): void {
    this.route.params.subscribe((params) => {
      this.workspaceId = params["id"];
    });
    this.route.queryParams.subscribe((params) => {
      this.workSpaceName = params["workSpaceName"];
      this.fileId = params["fileId"];
      this.es_id = params["es_id"];
      this.typeDocument = params["type"];
    });
    this.workSpaceService.isMaximized   
      .pipe(takeUntil(this.unSubAll))
      .subscribe((res) => {
        this.isMaximized = res;
      });
    this.chatbotService.openDrafting$
      .pipe(takeUntil(this.unSubAll), filter((v): v is { focus?: boolean } => !!v))
      .subscribe((opts) => {
        const draftingTool = this.listTool.find(t => t.value === 5);
        if (draftingTool && !this.toolSelected.some(t => t.value === 5)) {
          const simpleChip = {
            value: Number(draftingTool.value),
            label: draftingTool.label,
            icon: draftingTool.icon
          };
          this.toolSelected = [...this.toolSelected, simpleChip];
          this.isDraftMode = true;
        }
        if (this.toolsPop) this.toolsPop.close();

        if (opts?.focus === false) {
          this.isMaximized = true;
          this.workSpaceService.isMaximized.next(true);
        } else {
          this.openAndFocus();
        }
      });
    if (this.type == FormType.Chatbot) {
      this.isMaximized = true;
      const workspace_id = localStorage.getItem("workspace_id");
      this.workspaceId = workspace_id || "";
    }
    this.chatbotService.openChatbot$
      .pipe(takeUntil(this.unSubAll))
      .subscribe(() => this.openAndFocus());

    this.chatbotService.focusChatbot$
      .pipe(takeUntil(this.unSubAll))
      .subscribe(() => this.focusInput());
    this.chatbotService.textFormVanBan // text khi bôi đen trong toàn văn, người dùng bấm hỏi chatbot
      .pipe(takeUntil(this.unSubAll))
      .subscribe((res) => {
        if (res) {
          this.isHasTextFromVanBan = false;
          this.selection_text = res;
          // console.log(this.selection_text);

          // this.selectedConversation = null;
          // this.messages = [];
        } else {
          this.loadConversationsWithMessages();
        }
      });
    this.chatbotService.textBoiDen // text khi bôi đen trong toàn văn , k bấm hỏi chatbot
      .pipe(takeUntil(this.unSubAll))
      .subscribe((res) => {
        if (res) {
          this.isHasTextFromVanBan = true;
          this.selection_text = res;
        } else {
          this.isHasTextFromVanBan = false;
        }
      });
    this.chatbotService.listDocument
      .pipe(takeUntil(this.unSubAll))
      .subscribe((res) => {
        if (Array.isArray(res)) {
          this.listDocument = res.filter((item: any) => item.status == 1);
          const availableKeys = new Set(
            this.listDocument
              .map((doc: any) => this.getDocumentKey(doc))
              .filter((key): key is string => !!key)
          );
          // Remove selections that are no longer available
          this.selectedDocumentKeys.forEach((key) => {
            if (!availableKeys.has(key)) {
              this.selectedDocumentKeys.delete(key);
            }
          });
        } else {
          this.listDocument = []; // fallback nếu res null
          this.selectedDocumentKeys.clear();
        }
        this.syncSelectedDocuments();
      });


    this.getQuota();
    this.autoScroll = true; // Enable auto-scroll on init
    this.syncSelectedDocuments();
    this.setupResizeListener();
    const ua = navigator.userAgent || '';
    const platform = (navigator as any).userAgentData?.platform || navigator.platform || '';
    const isMobileUA = /Mobi|Android/i.test(ua);
    const isAndroidUA = /Android/i.test(ua);
    const isIOSUA = /iPhone|iPad|iPod/i.test(ua);

    const isAndroidPlatform =
      platform.toLowerCase().includes('android') ||
      (platform.toLowerCase().includes('linux') && 'ontouchstart' in window);

    const isIOSPlatform =
      platform.toLowerCase().includes('ios') ||
      (platform.toLowerCase().includes('mac') && navigator.maxTouchPoints > 1);

    if (isAndroidUA || isAndroidPlatform) {
      this.renderer.addClass(document.body, 'is-android');
      // console.log('Detected Android device ✅');
    } else if (isIOSUA || isIOSPlatform) {
      this.renderer.addClass(document.body, 'is-ios');
      // console.log('Detected iOS device ✅');
    } else {
      // console.log('Desktop or unknown device 🖥️', { ua, platform });
    }
  }
  
  /** Mở chatbot (nếu đang thu nhỏ) và focus vào ô nhập */
  public openAndFocus() {
    this.isMaximized = true;
    this.workSpaceService.isMaximized.next(true);
    this.focusInput();
  }

  /** Focus caret vào textarea nhập liệu */
  public focusInput() {
    setTimeout(() => {
      const el = this.queryInput?.nativeElement;
      if (!el) return;
      el.focus();
      // đưa caret về cuối (tránh select hết)
      const v = el.value;
      el.value = '';
      el.value = v;
    }, 0);
  }
  getOfficeIcon(document) {
    if (document?.types == TypeDocument.CSDL) {
      return "assets/images/icons/file-search.svg";
    } else if (document?.types == TypeDocument.FromSearch) {
      return "assets/images/icons/search.svg";
    } else {
      const fileName = document?.name;
      let fileExtension = null;

      if (typeof fileName === "string" && fileName.lastIndexOf(".") !== -1) {
        fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1);
      }

      if (fileExtension == "docx") return "assets/images/icons/docx.svg";
      if (fileExtension == "pdf") return "assets/images/icons/pdf.svg";
      if (fileExtension == "doc") return "assets/images/icons/doc.svg";
      return "assets/images/icons/docx.svg";
    }
  }
  getQuota() {
    this.chatbotService.getQuota().subscribe((res) => {
      this.quota = res.limit_request - res.request_count;
    });
  }
  loadConversationsWithMessages() {
    this.chatbotService
      .getConversation(this.workspaceId)
      .pipe(
        takeUntil(this.unSubAll),
        tap((res: any) => {
          this.listConversation = res;
          if (this.listConversation.length == 0) this.messages = [];
          this.groupConversations();
          this.selectedConversation = res[0];
        }),
        filter((res: any) => res.length > 0),
        switchMap((res: any) => {
          const firstConversationId = res[0].id;
          return this.chatbotService.getMessageChatbot(firstConversationId);
        }),
        tap((messageRes: any) => {
          this.messages = messageRes.messages.map((msg) => {
            const idx = msg.answer.indexOf("<answer>");
            if (idx !== -1) {
              const after = msg.answer.substring(idx + 8, idx + 9);
              if (after !== "\n") {
                msg.answer = msg.answer.replace("<answer>", "<answer>\n\n");
              }
            }
            return msg;
          });

          // ⭐ rebuild DRAFT từ dữ liệu server (case F5)
          this.messages.forEach((msg) => {
            if (msg.role === "assistant") {
              this.finalizeDraftDocForMessage(msg.id, msg.answer, {
                openCanvas: false,
                silent: true,
                streaming: false,
              });
            }
          });

          setTimeout(() => {
            this.scrollToBottom();
          }, 10);
        })
      )
      .subscribe();
  }
  groupConversations() {
    const now = new Date();
    const today: any[] = [];
    const last7Days: any[] = [];
    const last30Days: any[] = [];

    for (const item of this.listConversation) {
      const updateAt = new Date(
        item.updated_at ? item.updated_at : item.created_at
      );
      const diffInDays = Math.floor(
        (now.setHours(0, 0, 0, 0) - updateAt.setHours(0, 0, 0, 0)) /
          (1000 * 60 * 60 * 24)
      );
      if (diffInDays === 0) {
        today.push(item);
      } else if (diffInDays > 0 && diffInDays < 7) {
        last7Days.push(item);
      } else if (diffInDays >= 7 && diffInDays < 30) {
        last30Days.push(item);
      }
    }

    // Sắp xếp từng nhóm từ mới nhất đến cũ nhất
    const sortDesc = (a: any, b: any) =>
      new Date(b.updated_at ? b.updated_at : b.created_at).getTime() -
      new Date(a.updated_at ? a.updated_at : a.created_at).getTime();
    today.sort(sortDesc);
    last7Days.sort(sortDesc);
    last30Days.sort(sortDesc);

    this.groupedConversations = [
      { label: "Hôm nay", items: today },
      { label: "7 ngày trước", items: last7Days },
      { label: "30 ngày trước", items: last30Days },
    ];
    this.groupedConversationsFilter = this.groupedConversations;
  }

  async streamFromChatbot() {
    this.abortController = new AbortController();
    this.bodyMessage = this.messages.map((msg) => ({
      role: msg.role,
      content: msg.answer,
      selection_text: msg.selection_text || null,
      selected_save_files: msg.selected_files.save_files || [],
      selected_upload_files: msg.selected_files.upload_files || [],
    }));
    // console.log("bodyMessage", this.bodyMessage);

    try {
      let content = null;
      if (this.textAskChatbot) {
        content = {
          content: this.textAskChatbot,
          original_doc_type:
            this.typeDocument == "upload"
              ? "uploaded"
              : this.typeDocument == "searching"
              ? "database"
              : this.typeDocument == "search"
              ? "saved"
              : null,
          id_document: this.es_id ? this.es_id : this.fileId || null,
        };
      }
      const response = await fetch(`${environment.apichatbot}/chat`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("token")}`,
        },
        body: JSON.stringify({
          messages: this.bodyMessage,
          do_rewrite: false,
          stream: true,
          workspace_id: this.workspaceId,
          conversation_id: this.selectedConversation
            ? this.selectedConversation.id
            : null,
          selection_text: content,
          selected_upload_files: this.selected_upload_files,
          selected_save_files: this.selected_save_files,
          use_law_database: this.toolSelected.some((item) => item.value === 1),
          use_google_search: this.toolSelected.some((item) => item.value === 2),
          creative_mode: this.isCreativeMode, // Thêm tham số chế độ sáng tạo
        }),
      });

      if (!response.body) {
        console.error("❌ No response body");
        return;
      }
      if (response.ok) {
        if (this.workspaceId) {
          // // console.log("update");
          this.chatbotService.updateTimeWorkspace(this.workspaceId).subscribe();
        }
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder("utf-8");
      let buffer = "";
      let checkStartThinking = false;
      let checkStartAnswer = true; //fix tạm , bình thường là false, do AI kh trả về <answer></answer> nữa
      let thinkingText = "";
      let answerText = "";
      let id = "";
      this.checkDoneAnswer = false;
      // Reset scroll tracking when starting new stream
      if (this.chatContainer) {
        this.lastScrollTop = this.chatContainer.nativeElement.scrollTop;
        this.autoScroll = true;
      }
      // Reset spacer update tracking
      this.lastSpacerUpdate = 0;
      this.spacerUpdatePending = false;
      this.lastReplyHeight = 0;
      this.scrollToBottom2();
      while (true) {
        if (this.abortController.signal.aborted) {
          // console.log("🚫 Đã huỷ stream, dừng vòng lặp.");
          this.checkDoneAnswer = true;
          if (this.quota > 0) this.quota -= 1;

          break;
        }
        const { value, done } = await reader.read();
        if (done) {
          if (this.quota > 0) this.quota -= 1;
          this.checkDoneAnswer = true;

          const created = this.finalizeDraftDocForMessage(id, answerText, {
            openCanvas: true,
            silent: false,
            streaming: false,   // ⭐ kết thúc, build terms một lần
          });

          if (!created) {
            this.getMessageOnConversation(this.selectedConversation);
          }
          break;
        }
        buffer += decoder.decode(value, { stream: true });

        // // console.log("json", buffer);
        const [jsonObjects, remaining] = this.extractJsonObjects(buffer);
        buffer = remaining;

        for (const obj of jsonObjects) {
          // // console.log(obj);

          this.checkDoneAnswer = false;
          if (obj.status) {
            this.checkDoneThinking = false;
            this.statusThinking = obj.data.message;
            if (obj.status == "need_clarification") {
              this.checkDoneThinking = true;
              this.messages.push({
                id: id,
                thinking: null,
                answer: obj.data.message,
                role: "assistant",
              });
              this.checkDoneAnswer = true;
            }
          } else {
            this.checkDoneThinking = true;
          }

          // khi nhận được conversation_id thì sẽ load lại list hội thoại, TH tạo cuộc hội thoại mới
          if (obj.conversation_id && this.selectedConversation == null) {
            this.selectedConversation = {
              id: obj.conversation_id,
            };
            this.chatbotService
              .getConversation(this.workspaceId)
              .subscribe((res: any) => {
                this.listConversation = res;
                this.groupConversations();
                // this.selectedConversation = res[0];
              });

            // this.loadConversationsWithMessages();
          }
          if (obj.id) {
            id = obj.id;
          }
          const apiTitle =
            obj.data?.title || obj.doc_title || obj.file_title || null; // tuỳ backend
          if (apiTitle && id) {
            const msgIdx = this.messages.findIndex((m) => m.id === id);
            if (msgIdx !== -1) {
              this.messages[msgIdx].apiTitle = apiTitle;
            } else {
              // Trường hợp message chưa được push, gắn luôn meta vào
              this.messages.push({
                id,
                role: "assistant",
                thinking: "",
                answer: "",
                apiTitle,
              });
            }
          }
          
          let text = "";
          if (obj.text) {
            text = obj.text || "";
          }
          this.collectDraftChunk(text);
          // // Handle thinking
          if (this.checkStartThinking(text)) checkStartThinking = true;
          else if (this.checkStartThinking(text) === false)
            checkStartThinking = false;
          if (checkStartThinking) thinkingText += text;

          // Handle answer  cmt tạm vào, sau này check do AI kh trả về <answer></answer> nữa
          // if (this.checkStartAnswer(text)) {
          //   checkStartAnswer = true;
          // } else if (this.checkStartAnswer(text) === false)
          //   checkStartAnswer = false;

          if (checkStartAnswer) {
            if (text.includes("<answer>")) {
              answerText += text + "\n\n"; // check trường hợp markdown đầu tiên cần thêm 1 cái \n nếu không nó kh nhận
            } else {
              if (answerText === "") {
                answerText += "\n" + text;
              } else {
                answerText += text;
              }
              // answerText += text;
            }
          }

          this.thinkingText = thinkingText;
          this.answerText = answerText;

          // Update to message list
          const index = this.messages.findIndex((m) => m.id === id);
          if (index !== -1) {
            this.messages[index].thinking = this.thinkingText;
            this.messages[index].answer = this.answerText;
            this.messages[index].role = "assistant";
          } else {
            this.messages.push({
              id: id,
              thinking: this.thinkingText,
              answer: this.answerText,
              role: "assistant",
            });
          }
          // this.updateDraftDocStreaming(id);
          this.handleDraftStreamingDuringStream(id);
          // if (text && id) {
          //    this.handleDraftStreamingDuringStream(id);
          // }
        }
        // Đợi view render tin nhắn phản hồi xong - sử dụng requestAnimationFrame để tránh layout thrashing
        if (!this.spacerUpdatePending) {
          this.spacerUpdatePending = true;
          requestAnimationFrame(() => {
            const replyElements = this.replyMessages.toArray();
            const lastReplyEl = replyElements[replyElements.length - 1];

            if (lastReplyEl && this.chatContainer) {
              const replyHeight = lastReplyEl.nativeElement.offsetHeight;
              const now = Date.now();
              const timeSinceLastUpdate = now - this.lastSpacerUpdate;
              const heightDiff = Math.abs(replyHeight - this.lastReplyHeight);
              
              // Chỉ update spacer khi:
              // 1. Có thay đổi đáng kể về chiều cao (> 20px), hoặc
              // 2. Đã qua 150ms kể từ lần update cuối, hoặc
              // 3. Spacer sắp hết (<= 50px)
              const shouldUpdate = heightDiff > 20 || 
                                   timeSinceLastUpdate > 150 || 
                                   (this.chatHeight <= 50 && replyHeight > this.lastReplyHeight);
              
              if (replyHeight > 0 && shouldUpdate) {
                const newChatHeight = Math.max(0, this.initialSpacerHeight - replyHeight);
                
                // Chỉ update nếu có thay đổi đáng kể
                if (Math.abs(newChatHeight - this.chatHeight) > 5) {
                  this.chatHeight = newChatHeight;
                  this.lastSpacerUpdate = now;
                  this.lastReplyHeight = replyHeight;
                }
                
                // Chỉ scroll khi spacer đã hết hoặc nội dung đã đủ lớn
                if (this.chatHeight <= 0 || replyHeight > 100) {
                  this.scrollToBottom();
                }
              }
            }
            this.spacerUpdatePending = false;
          });
        }

        // this.scrollToBottom();
      }
    } catch (error) {
      this.checkDoneAnswer = true;
      this.checkDoneThinking = true;
      // this.toast.error("Thất bại", "Có lỗi xảy ra trong quá trình thực hiện!", {
      //   closeButton: true,
      //   positionClass: "toast-top-right",
      //   toastClass: "toast ngx-toastr",
      // });
    } finally {
      this.abortController = null;
    }
  }
  extractJsonObjects(str: string): any[] {
    const objects: any[] = [];
    let depth = 0;
    let start = -1;
    let inString = false;
    let escape = false;

    for (let i = 0; i < str.length; i++) {
      const char = str[i];

      if (char === '"' && !escape) {
        inString = !inString;
      }

      if (!inString) {
        if (char === "{") {
          if (depth === 0) start = i;
          depth++;
        } else if (char === "}") {
          depth--;
          if (depth === 0 && start !== -1) {
            const jsonStr = str.slice(start, i + 1);
            try {
              objects.push(JSON.parse(jsonStr));
            } catch (e) {
              console.warn("⚠️ JSON parse failed", jsonStr);
            }
            start = -1;
          }
        }
      }

      escape = char === "\\" && !escape;
    }

    const remaining = depth > 0 && start !== -1 ? str.slice(start) : "";
    return [objects, remaining];
  }

  checkStartThinking(text) {
    if (text.includes("<think>")) return true;
    else if (text.includes("<answer>")) return false;
  }
  checkStartAnswer(text) {
    if (text.includes("<answer>")) return true;
    else if (text.includes("</answer>")) return false;
  }

  minimumChatbot() {
    this.isMaximized = false;
    this.workSpaceService.isMaximized.next(false);

    // Nếu là màn hình nhỏ thì gọi toggleNotes để thu gọn
    if (this.isSmallScreen()) {
      this.workSpaceService.shouldToggleNotes.next(true);
    }
  }

  // Hàm kiểm tra màn hình nhỏ (dưới 1200px)
  private isSmallScreen(): boolean {
    return window.innerWidth < 1200;
  }

  autoResize(event: Event) {
    const textarea = event.target as HTMLTextAreaElement;
    textarea.style.height = "auto"; // Reset height
    textarea.style.height = textarea.scrollHeight + "px"; // Set to scroll height
  }
  sendMessage(event: KeyboardEvent) {
    if (event.key === "Enter" && !event.shiftKey && this.quota != 0) {
      event.preventDefault(); // Ngăn xuống dòng khi nhấn Enter
      if (this.userInput.trim() && this.checkDoneAnswer) {
        this.textAskChatbot = this.selection_text;
        this.isHasTextFromVanBan = false;
        this.selection_text = null;
        this.checkDoneAnswer = false;

        this.messages.push({
          id: new randomUuidv4().randomUuidv4(),
          thinking: "",
          answer: this.userInput.trim(),
          role: "user",
          selection_text: this.textAskChatbot ? this.textAskChatbot : null,
          selected_files: {
            upload_files: this.selectedFile ? this.selectedFile : null,
          },
        });
        // this.selectedFile = [];
        // this.selected_save_files = [];
        // this.selected_upload_files = [];
        if (this.chatContainer) {
          this.initialSpacerHeight = this.chatContainer.nativeElement.clientHeight - 100;
          this.chatHeight = this.initialSpacerHeight;
        }

        setTimeout(() => this.scrollToBottom(), 10);
        // setTimeout(() => this.scrollToBottom(), 10);
        // setTimeout(() => this.scrollToBottom(), 20);
        // Nếu là lệnh soạn thảo nội bộ => tạo local doc & KHÔNG gọi API
        // if (this._maybeHandleLocalDrafting(this.userInput.trim())) {
        //   this.userInput = "";
        //   this.resetTextareaHeight();
        //   return;
        // }
        // if (this.isDraftMode) {
        //   this.handleDraftingMessage(this.userInput.trim(), this.messages[this.messages.length - 1]?.id);
        //   this.userInput = "";
        //   this.selection_text = null;
        //   this.resetTextareaHeight();
        //   return;
        // }
        // MOCK: nếu người dùng gõ "mock soạn thảo" thì không gọi API, mà dùng dữ liệu giả
const trimmed = this.userInput.trim();
if (trimmed.toLowerCase() === 'mock soạn thảo') {
  // tạo 1 message user như bình thường
  // this.messages.push({
  //   id: new randomUuidv4().randomUuidv4(),
  //   thinking: "",
  //   answer: trimmed,
  //   role: "user",
  //   selection_text: this.textAskChatbot ? this.textAskChatbot : null,
  //   selected_files: {
  //     upload_files: this.selectedFile ? this.selectedFile : null,
  //   },
  // });

  // if (this.chatContainer) {
  //   this.chatHeight = this.chatContainer.nativeElement.clientHeight - 100;
  // }

  // setTimeout(() => this.scrollToBottom(), 10);

  // Gọi mock thay vì gọi API thật
  this.runMockTripleBlock();

  this.userInput = "";
  this.selection_text = null;
  this.resetTextareaHeight();
  return;
}

        this.streamFromChatbot();
        this.userInput = "";
      }
      this.resetTextareaHeight(); // 👉 Gọi reset chiều cao
    }
  }
  clickSendMessage() {
    if (this.userInput.trim() && this.checkDoneAnswer && this.quota != 0) {
      this.checkDoneAnswer = false;
      this.textAskChatbot = this.selection_text;
      this.isHasTextFromVanBan = false;
      this.messages.push({
        id: new randomUuidv4().randomUuidv4(),
        thinking: "",
        answer: this.userInput.trim(),
        role: "user",
        selection_text: this.textAskChatbot ? this.textAskChatbot : null,
        selected_files: {
          upload_files: this.selectedFile ? this.selectedFile : null,
        },
      });
      // this.selectedFile = [];
      // this.selected_save_files = [];
      // this.selected_upload_files = [];
      if (this.chatContainer) {
        this.initialSpacerHeight = this.chatContainer.nativeElement.clientHeight - 100;
        this.chatHeight = this.initialSpacerHeight;
      }
      setTimeout(() => this.scrollToBottom(), 0);
      // Nếu là lệnh soạn thảo nội bộ => tạo local doc & KHÔNG gọi API
      // if (this._maybeHandleLocalDrafting(this.userInput.trim())) {
      //   this.userInput = "";
      //   this.selection_text = null;
      //   this.resetTextareaHeight();
      //   return;
      // }
      // if (this.isDraftMode) {
      //   this.handleDraftingMessage(this.userInput.trim(), this.messages[this.messages.length - 1]?.id);
      //   this.userInput = "";
      //   this.selection_text = null;
      //   this.resetTextareaHeight();
      //   return;
      // }
      this.streamFromChatbot();
      this.userInput = "";
      this.selection_text = null;
    }
    this.resetTextareaHeight(); // 👉 Gọi reset chiều cao
  }
  resetTextareaHeight() {
    const textarea = document.getElementById(
      "queryChatbot"
    ) as HTMLTextAreaElement;
    if (textarea) {
      textarea.style.height = "auto"; // Reset lại chiều cao mặc định
    }
  }
  scrollToBottom(): void {
    try {
      if (this.autoScroll) {
        const container = this.chatContainer.nativeElement;
        // Cuộn thẳng xuống cuối cùng
        container.scrollTop = container.scrollHeight;
      }
    } catch (err) {
      console.error("Scroll failed:", err);
    }
  }
  scrollToBottom2(): void {
    try {
      const container = this.chatContainer.nativeElement;
      // Cuộn thẳng xuống cuối cùng
      container.scrollTop = container.scrollHeight;
    } catch (err) {
      console.error("Scroll failed:", err);
    }
  }
  cancelRequest() {
    this.checkDoneAnswer = true;
    if (this.abortController) {
      this.abortController.abort();
    }
  }
  addNewChat() {
    this.messages = []; // Clear messages for new chat
    // this.chatbotService.saveMessageChatbot.next([]);
    this.selectedConversation = null;
    if (window.innerWidth <= 768 && this.isMobileSidebarOpen) {
      this.toggleMobileSidebar();
    }
  }
  isMobileView(): boolean {
    return window.innerWidth <= 768;
  }
  onConversationChange(conversation: any) {
    this.selectedConversation = conversation;
    this.getMessageOnConversation(conversation);
    if (window.innerWidth <= 768) {
      this.toggleMobileSidebar();
    }
  }
  getMessageOnConversation(conversation: any) {
    this.chatbotService
      .getMessageChatbot(conversation.id)
      .pipe(
        takeUntil(this.unSubAll),
        tap((messageRes: any) => {
          this.messages = messageRes.messages.map((msg) => {
            const idx = msg.answer.indexOf("<answer>");
            if (idx !== -1) {
              const after = msg.answer.substring(idx + 8, idx + 9);
              if (after !== "\n") {
                msg.answer = msg.answer.replace("<answer>", "<answer>\n\n");
              }
            }
            return msg;
          });

          // ⭐ rebuild DRAFT khi đổi conversation
          this.messages.forEach((msg) => {
            if (msg.role === "assistant") {
              this.finalizeDraftDocForMessage(msg.id, msg.answer, {
                openCanvas: false,
                silent: true,
                streaming: false,
              });
            }
          });

          this.scrollToBottom();
        })
      )
      .subscribe();
  }
  onRightClickChatbotMessage(event: MouseEvent, item): void {
    event.preventDefault();
    this.contextMenuVisible = true;
    this.contextMenuPosition = { x: event.clientX, y: event.clientY };
    this.contextMenuItem = item;
  }
  closeContextMenu(): void {
    this.contextMenuVisible = false;
  }
  @HostListener("document:click", ["$event"])
  onDocumentClick(event: MouseEvent): void {
    this.closeContextMenu();
    const target = event.target as HTMLElement;
    if (!target) return;
    const isClickOnChatbotFile =
      target.closest('.file-item') ||
      target.closest('.chatbot-file-card');
    if (!isClickOnChatbotFile) {
      this.activeFileKey = null;
    }
    
    // Close submenu if clicking outside the popover
    if (this.activeSubmenu !== null) {
      const target = event.target as HTMLElement;
      // Check if click is inside the popover content (including submenu) or tool button
      const popover = document.querySelector('.popover');
      const popoverBody = document.querySelector('.popover-body, .popover .text-dark');
      const toolButton = document.querySelector('[ngbpopover]');
      
      const isInsidePopover = (popover && popover.contains(target)) || 
                              (popoverBody && popoverBody.contains(target));
      const isInsideToolButton = toolButton && toolButton.contains(target);
      
      // If click is outside both popover and tool button, close submenu
      if (!isInsidePopover && !isInsideToolButton) {
        this.activeSubmenu = null;
        this.submenuOpenedByClick = false; // Reset flag when closing
      }
    }
  }

  selectedForRename: any;
  renameChatbot(contextMenuItem) {
    this.isEditChatbotName = true;
    this.selectedForRename = contextMenuItem;
    setTimeout(() => {
      const elements = document.querySelectorAll("[contenteditable]");
      elements.forEach((el) => {
        if (el.textContent?.trim() === contextMenuItem.name) {
          // Move caret to end
          const range = document.createRange();
          const sel = window.getSelection();
          range.selectNodeContents(el);
          range.collapse(false);
          sel?.removeAllRanges();
          sel?.addRange(range);
        }
      });
    }, 0);
  }
  onEditNameEnter(event: KeyboardEvent, item: any) {
    event.preventDefault(); // Ngăn xuống dòng
    const newName = (event.target as HTMLElement).innerText.trim();
    this.saveNewName(item, newName);
  }

  onEditNameBlur(event: FocusEvent, item: any) {
    const newName = (event.target as HTMLElement).innerText.trim();
    this.saveNewName(item, newName);
  }

  saveNewName(item: any, newName: string) {
    if (newName !== item.name) {
      if (newName.length > 255 || newName.length == 0) {
        this.toast.error(
          newName.length > 255
            ? "Không được vượt quá 255 ký tự!"
            : "Tên không được để trống!",
          "Lỗi",
          {
            closeButton: true,
            positionClass: "toast-top-right",
            toastClass: "toast ngx-toastr",
          }
        );
        // Reset lại tên nếu quá dài hoặc để trống
        this.groupConversations();
        this.isEditChatbotName = false;
        return;
      } else {
        item.name = newName;
      }
      this.chatbotService.renameChatbot(item.id, newName).subscribe(
        (res) => {
          this.toast.success("Đã cập nhật tên chatbot", "Thành công", {
            closeButton: true,
            positionClass: "toast-top-right",
            toastClass: "toast ngx-toastr",
          });
          item.updated_at = new Date().toISOString(); // Cập nhật thời gian sửa đổi
          this.groupConversations();
        },
        (err) => {
          // Luôn trả lại tên ban đầu nếu đổi tên thất bại
          this.groupConversations();

          this.toast.error("Có lỗi xảy ra khi cập nhật tên chatbot!", "Lỗi", {
            closeButton: true,
            positionClass: "toast-top-right",
            toastClass: "toast ngx-toastr",
          });
        }
      );
    }
    this.isEditChatbotName = false;
  }
  deleteChatbot(contextMenuItem) {
    Swal.fire({
      title: "Bạn có chắc chắn muốn xóa?",
      icon: "warning",
      reverseButtons: true,
      showCancelButton: true,
      confirmButtonColor: "#008fd3",
      cancelButtonColor: "#EEE",
      customClass: {
        confirmButton: "swal-confirm",
        cancelButton: "swal-cancel",
      },
      confirmButtonText: "Xóa",
      cancelButtonText: "Hủy",

      preConfirm: async () => {
        return this.chatbotService.deleteChatbot(contextMenuItem.id).subscribe(
          (res) => {
            this.toast.success("Đã xoá lịch sử chat", "Thành công", {
              closeButton: true,
              positionClass: "toast-top-right",
              toastClass: "toast ngx-toastr",
            });
            this.loadConversationsWithMessages();
          },
          (err) => {
            this.toast.error("Có lỗi xảy ra khi xoá lịch sử chat!", "Lỗi", {
              closeButton: true,
              positionClass: "toast-top-right",
              toastClass: "toast ngx-toastr",
            });
          }
        );
      },

      allowOutsideClick: () => {
        return !Swal.isLoading();
      },
    });
  }
  filterConversations() {
    const search = this.searchConversationString?.trim().toLowerCase() || "";

    if (!search || search.length === 0) {
      this.groupedConversationsFilter = [...this.groupedConversations];
      return;
    }

    this.groupedConversationsFilter = this.groupedConversations
      .map((group) => ({
        ...group,
        items: group.items.filter((item) =>
          item.name.toLowerCase().includes(search)
        ),
      }))
      .filter((group) => group.items.length > 0);
  }

  onAnswerClick(event: MouseEvent): void {
    this.viewDetailFileService.clauseId.next(null); // reset clauseID khi nhấn vào thẻ a
    // sự kiện click vào thẻ a
    const target = event.target as HTMLElement;
    // console.log(target);
    // Kiểm tra nếu phần tử được click là thẻ <a>
    if (target.tagName.toLowerCase() === "a") {
      const linkText = target.textContent?.trim();
      const typeLink = target.getAttribute("href");
    // ====== MỞ VĂN BẢN ẢO (local:<id>) ======
    if (typeLink?.startsWith('local:')) {
      const localId = typeLink.split(':').pop();
      const file = this.chatbotService.getLocalDoc(localId);
      if (!file) { event.preventDefault(); return; }

      const fakeDoc: any = {
        id: file.id,
        trich_yeu: file.trich_yeu || file.name,
        loai_van_ban: file.loai_van_ban || 'Văn bản',
        so_hieu: file.so_hieu || '',
        co_quan_ban_hanh: file.co_quan_ban_hanh || '',
        tinh_trang_hieu_luc: file.tinh_trang_hieu_luc || '',
        toan_van: file.toan_van,
        terms: file.terms || [],
        label: 'Tất cả',
        types: 3,
        name: file.name
      };

      this.viewDetailFileService.fileInfor.next(fakeDoc);
      this.router.navigate([], {
        queryParams: {
          fileId: file.id,
          tabs: "toanvan",
          type: "searching",
          time: new Date().getTime(),
          fileName: this.transform(file.trich_yeu || file.name),
          save: true,
          // 👇 thêm dòng này
          openMode: "chatbot-draft",
        },
        queryParamsHandling: "merge",
      });

      this.listDocumentService.FileSearchTemp.next(fakeDoc);
      this.listDocumentService.setBehavior(ShowContent.Search);

      if (this.isMaximized || this.type === FormType.Chatbot) {
        try { this.modalService?.dismissAll(); } catch {}
        this.openCanvas(fakeDoc);
      }
      event.preventDefault();
      return;
    }
    // =========================================

      if (typeLink.includes("legal")) {
        if (
          linkText.toLowerCase().includes("điều") ||
          linkText.toLowerCase().includes("khoản") ||
          linkText.toLowerCase().includes("điểm")
        ) {
          // ưu tiên tìm kiếm điều
          this.chatbotService
            .getDocIdFromSoHieu(linkText, true)
            .subscribe((res) => {
              if (!this.handleFileResult(res)) {
                this.chatbotService
                  .getDocIdFromSoHieu(linkText, false)
                  .subscribe((res2) => {
                    // this.viewDetailFileService.clauseId.next("dieu_1"); // để khi tìm điều khoản không ra sẽ nhảy sang văn bản, và chuyển scroll về điều 1 tránh hiểu lầm
                    if (!this.handleFileResult(res2)) {
                      this.toast.error(
                        "Không tìm thấy văn bản nào với số hiệu này",
                        "Lỗi",
                        {
                          closeButton: true,
                          positionClass: "toast-top-right",
                          toastClass: "toast ngx-toastr",
                        }
                      );
                    }
                  });
              }
            });
          // console.log("Bạn đã click vào <a> có text:", linkText);
        } else {
          // ưu tiên tìm kiếm văn bản
          this.chatbotService
            .getDocIdFromSoHieu(linkText, false)
            .subscribe((res) => {
              if (!this.handleFileResult(res)) {
                this.chatbotService
                  .getDocIdFromSoHieu(linkText, true)
                  .subscribe((res2) => {
                    // this.viewDetailFileService.clauseId.next("dieu_1"); // để khi tìm điều khoản không ra sẽ nhảy sang văn bản, và chuyển scroll về điều 1 tránh hiểu lầm
                    if (!this.handleFileResult(res2)) {
                      this.toast.error(
                        "Không tìm thấy văn bản nào với số hiệu này",
                        "Lỗi",
                        {
                          closeButton: true,
                          positionClass: "toast-top-right",
                          toastClass: "toast ngx-toastr",
                        }
                      );
                    }
                  });
              }
            });
          // console.log("Bạn đã click vào <a> có text:", linkText);
        }
      } else {
        const idFile = typeLink.split(":").pop();
        this.router.navigate([], {
          queryParams: {
            fileId: idFile,
            tabs: "toanvan",
            type: "searching",
            time: new Date().getTime(),
            fileName: this.transform(linkText),
            save: true,
          },
          queryParamsHandling: "merge",
        });
        this.listDocumentService.FileSearchTemp.next(null);

        this.listDocumentService.setBehavior(ShowContent.Search);
      }
      // // console.log("link", linkText);

      // Nếu muốn chặn chuyển trang mặc định:
      event.preventDefault();
      // Xử lý logic tại đây...
    }
  }
  private runMockTripleBlock(): void {
    const id = new randomUuidv4().randomUuidv4();

    const answerText = `<answer>

  Dưới đây là dự thảo:

  <document>
  Quyết định phê duyệt đề án ABC
  Căn cứ Luật Tổ chức chính quyền địa phương ngày ...
  Căn cứ Nghị định số 37/2025/NĐ-CP ngày ...
  Điều 1. Phê duyệt đề án ABC với các nội dung chủ yếu sau: ...
  Điều 2. Giao Sở Kế hoạch và Đầu tư chủ trì, phối hợp với các đơn vị liên quan tổ chức triển khai thực hiện đề án.
  Điều 3. Quyết định này có hiệu lực kể từ ngày ký.

  TM. ỦY BAN NHÂN DÂN
  CHỦ TỊCH
  Nguyễn Văn A
  </document>

  Ghi chú thêm của chatbot: Anh có thể rà soát lại tên đề án và căn cứ pháp lý cho phù hợp thực tế.
  </answer>`;

    this.messages.push({
      id,
      role: "assistant",
      thinking: "",
      answer: answerText,
    });

    this.checkDoneAnswer = true;

    this.finalizeDraftDocForMessage(id, answerText);

    setTimeout(() => this.scrollToBottom(), 10);
  }

  private handleFileResult(res: any) {
    const file = res.data[0];
    if (res.data.length > 0) {
      this.viewDetailFileService.fileInfor.next(file);
      this.router.navigate([], {
        queryParams: {
          fileId: file.id,
          tabs: "toanvan",
          type: "searching",
          time: new Date().getTime(),
          fileName: this.transform(file.trich_yeu),
          save: true,
          openMode: null,
        },
        queryParamsHandling: "merge",
      });
      this.listDocumentService.FileSearchTemp.next(file);
      this.listDocumentService.setBehavior(ShowContent.Search);
      if (file.terms.length > 0) {
        // nếu có điều khoản thì sẽ lấy điều khoản đầu tiên để hiển thị, còn k có thì thôi
        const termId = file.terms[0].term_id;
        this.viewDetailFileService.clauseId.next(termId);
      }

      // this.isMaximized = false;
      // if (this.type == FormType.Chatbot) {
      //   this.modalOpen(this.viewFileModal, FormType.Convert, "xl");
      //   this.isMaximized = true;
      // }
      // if (this.isMaximized && this.type != FormType.Chatbot) {
      //   this.modalOpen(this.viewFileModal, FormType.Maximized, "xl");
      //   // this.isMaximized = true;
      // }
      // if (this.type == FormType.Chatbot) {
      //   this.openDocViewerLarge();
      // } else if (this.isMaximized) {
      //   this.openDocViewerLarge();
      // }
      if (this.isMaximized) {
        this.openCanvas(file);
      } else if (this.type === FormType.Chatbot) {
        // phòng trường hợp mở từ vị trí khác
        this.openCanvas(file);
      }
      return true;
    }
    return false;
  }
  private handleDraftingMessage(prompt: string, lastUserMsgId?: string) {
    // Chuẩn bị buffer bắt block '''...'''
    this.draftRawBuffer = "";
    this.draftBlockContent = null;
    // lastUserMsgId là id tin nhắn user vừa push, dùng để map sang answer nếu cần
    // Ở đây ta chỉ cần gọi lại streamFromChatbot như bình thường
    this.streamFromChatbot();
  }
  private collectDraftChunk(chunk: string) {
    if (!this.isDraftMode || !chunk) return;

    this.draftRawBuffer += chunk;

    const startTag = "<document>";
    const endTag = "</document>";

    const start = this.draftRawBuffer.indexOf(startTag);
    if (start === -1) return;

    const end = this.draftRawBuffer.indexOf(endTag, start + startTag.length);
    if (end === -1) return; // chưa đóng </document>

    const inside = this.draftRawBuffer.substring(start + startTag.length, end);
    this.draftBlockContent = inside.trim();
  }

  private handleDraftStreamingDuringStream(messageId: string) {
    if (!messageId) return;

    const idx = this.messages.findIndex((m) => m.id === messageId);
    if (idx === -1) return;

    const msg = this.messages[idx];

    const raw = this.answerText || "";

    const docIdx = raw.indexOf("<document>");
    const fenceIdx = raw.indexOf("```");

    let cutIdx = -1;
    if (docIdx !== -1) cutIdx = docIdx;
    else if (fenceIdx !== -1) cutIdx = fenceIdx;

    if (cutIdx !== -1) {
      const top = raw.slice(0, cutIdx).trim();
      this.messages[idx].answerTop = top || null;
      this.messages[idx].answer = top || "";
    }

    const hadFileBefore = !!msg.fileAttached;

    this.finalizeDraftDocForMessage(messageId, raw, {
      openCanvas: false,
      silent: false,
      streaming: true,
    });

    if (!hadFileBefore && msg.fileAttached) {
      this.viewFileAttached(msg.fileAttached);
    }
  }
  private findChatbotDocById(id: string): any | null {
    if (!id) return null;
    const docs = this.chatbotService.listDocument.getValue() || [];

    return docs.find((doc: any) => {
      const key =
        doc?.id ??
        doc?.doc_id ??
        doc?.es_id ??
        doc?.fileId ??
        null;

      return key !== null && String(key) === String(id);
    }) || null;
  }


  private _extractFirstTripleBlockFromAnswer(full: string): {
    cleanedAnswer: string;
    title: string;
    content: string;
    prefix: string;
    suffix: string;
  } | null {
    if (!full) return null;

    // 1. ƯU TIÊN tag <document>...</document>
    const startTag = "<document>";
    const endTag = "</document>";

    let start = full.indexOf(startTag);
    if (start !== -1) {
      let end = full.indexOf(endTag, start + startTag.length);
      if (end === -1) {
        // chưa có </document> thì lấy đến hết chuỗi
        end = full.length;
      }

      const inner = full.slice(start + startTag.length, end);
      const prefix = full.slice(0, start);
      const suffix = end !== full.length ? full.slice(end + endTag.length) : "";

      const content = inner.trim();
      const firstLine = (content.split(/\r?\n/)[0] || "Văn bản soạn thảo").trim();
      const title = firstLine || "Văn bản soạn thảo";

      const cleanedAnswer = (prefix + suffix).trim();

      return {
        cleanedAnswer,
        title,
        content,
        prefix: prefix.trimEnd(),
        suffix: suffix.trimStart(),
      };
    }

    let s = full;

    let blockStart = s.indexOf("```");
    let fence = "```";

    if (blockStart === -1) {
      blockStart = s.indexOf('"""');
      fence = '"""';
    }

    if (blockStart === -1) return null;

    const afterFence = s.slice(blockStart + fence.length);
    const endRel = afterFence.indexOf(fence);

    let blockFullText: string;
    let inner: string;

    if (endRel === -1) {
      blockFullText = s.slice(blockStart);
      inner = afterFence;
    } else {
      blockFullText = s.slice(
        blockStart,
        blockStart + fence.length + endRel + fence.length
      );
      inner = afterFence.slice(0, endRel);
    }

    const prefix = s.slice(0, blockStart);
    const suffix = s.slice(blockStart + blockFullText.length);

    const content = inner.trim();
    const firstLine = (content.split(/\r?\n/)[0] || "Văn bản soạn thảo").trim();
    const title = firstLine || "Văn bản soạn thảo";

    const cleanedAnswer = (prefix + suffix).trim();

    return {
      cleanedAnswer,
      title,
      content,
      prefix: prefix.trimEnd(),
      suffix: suffix.trimStart(),
    };
  }

  private _buildDraftHtmlFromTitleAndContent(
    title: string,
    content: string
  ): { title: string; html: string } {
    const text = (content || "").trim();

    // Tách từng dòng, bỏ bớt dòng rỗng ở đầu/cuối
    const lines = text.split(/\r?\n/);
    const cleanLines = lines.map((l) => l.trimEnd());

    // Dòng đầu tiên không rỗng xem như title, các dòng sau là nội dung
    const firstNonEmptyIndex = cleanLines.findIndex(
      (l) => l.trim() !== ""
    );
    let titleLine = "";
    let bodyLines: string[] = [];

    if (firstNonEmptyIndex === -1) {
      titleLine = title || "Văn bản soạn thảo";
      bodyLines = [];
    } else {
      titleLine =
        cleanLines[firstNonEmptyIndex] ||
        title ||
        "Văn bản soạn thảo";
      bodyLines = cleanLines.slice(firstNonEmptyIndex + 1);
    }

    const esc = (s: string) =>
      (s || "")
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;");

    let termOrder = 1;
    const getHeadingId = (rawLine: string): string | null => {
      const t = rawLine.trim();
      if (!t) return null;

      // Giống logic trong _buildDraftTermsFromPlain
      const isRoman = /^[IVXLC]+\.\s+.+/i.test(t);
      const isNumber = /^\d+\.\s+.+/.test(t);
      const isChapter = /^Chương\s+\w+/i.test(t);
      const isMucLuc = /^Mục lục$/i.test(t);

      if (isRoman || isNumber || isChapter || isMucLuc) {
        const id = `draft_term_${termOrder
          .toString()
          .padStart(3, "0")}`;
        termOrder++;
        return id;
      }
      return null;
    };

    const titleHtml = `
      <h1
        style="
          font-size: 16px;
          font-weight: bold;
          margin: 0 0 12px 0;
        "
      >
        ${esc(titleLine)}
      </h1>
    `;

    const bodyHtml = bodyLines
      .map((line) => {
        const trimmed = line.trim();

        if (!trimmed) {
          return `<p style="font-size: 14px; margin: 0 0 8px 0;">&nbsp;</p>`;
        }

        // nếu là dòng đầu mục (I., II., Điều 1., Mục 1....) thì gắn id draft_term_00x
        const headingId = getHeadingId(trimmed);
        const idAttr = headingId ? ` id="${headingId}"` : "";

        return `
          <p
            ${idAttr}
            style="
              font-size: 14px;
              margin: 0 0 8px 0;
            "
          >
            ${esc(trimmed)}
          </p>
        `;
      })
      .join("");

    const html = `
      <div
        style="
          font-family: 'Times New Roman', serif;
          line-height: 1.4;
          margin: 0;
          background-color: #ffffff;
          color: #000000;
        "
      >
        ${titleHtml}
        ${bodyHtml}
      </div>
    `.trim();

    return {
      title: title || titleLine || "Văn bản soạn thảo",
      html,
    };
  }

  private _buildDraftTermsFromPlain(raw: string): any[] {
    if (!raw) return [];

    const lines = raw.split(/\r?\n/);
    const terms: any[] = [];
    let order = 1;

    for (const line of lines) {
      const trimmed = (line || "").trim();
      if (!trimmed) continue;

      const t = trimmed.replace(/^[-–•*]\s+/, "");
      if (!t) continue;

      const isRoman   = /^[IVXLC]+\.\s+.+/i.test(t);
      const isNumber  = /^\d+\.\s+.+/.test(t);
      const isChapter = /^Chương\s+\w+/i.test(t);
      const isMucLuc  = /^Mục lục$/i.test(t);

      if (isRoman || isNumber || isChapter || isMucLuc) {
        terms.push({
          term_id: `draft_term_${order.toString().padStart(3, "0")}`,
          name: t,
          title: t,
          order,
          status: 1,
          is_reviewed: true,
        });
        order++;
      }
    }

    return terms;
  }

  private finalizeDraftDocForMessage(
    messageId: string | number | null,
    fullAnswer: string,
    opts?: { openCanvas?: boolean; silent?: boolean; streaming?: boolean; docxUrl?: string | null;}
  ): boolean {
    if (!messageId || !fullAnswer) return false;

    const { openCanvas = true, silent = false, streaming = false, docxUrl = null } = opts || {};

    const idx = this.messages.findIndex((m) => m.id === messageId);
    if (idx === -1) return false;

    const msg = this.messages[idx];

    // Tách block ```...``` ra khỏi answer
    const block = this._extractFirstTripleBlockFromAnswer(fullAnswer);
    if (!block) return false;

    // Build HTML + title
    const built = this._buildDraftHtmlFromTitleAndContent(
      block.title,
      block.content
    );
    const terms = !streaming
      ? this._buildDraftTermsFromPlain(block.content)
      : undefined;

    let doc: any;

    if (msg.fileAttached) {
      doc = msg.fileAttached;

      doc.name = built.title;          // tên file = tiêu đề
      doc.trich_yeu = built.title;
      doc.toan_van = built.html;
      doc.loai_van_ban = doc.loai_van_ban || "Văn bản soạn thảo";
      doc.label = built.title;
      doc.so_hieu = doc.so_hieu || "";
      doc.co_quan_ban_hanh = doc.co_quan_ban_hanh || "";
      doc.tinh_trang_hieu_luc = doc.tinh_trang_hieu_luc || "";

      if (!streaming && terms) {
        doc.terms = terms;
      }

      if (!doc.created_at && !doc.createdAt && !doc.updated_at) {
        doc.created_at = new Date().toISOString();
      }

      (doc as any).__chatbotDraft = true;
    } else {
      // chưa có file -> tạo mới trong local store
      const created = this.chatbotService.createLocalDoc({
        name: built.title,
        label: built.title,
        trich_yeu: built.title,
        toan_van: built.html,
        loai_van_ban: "Văn bản soạn thảo",
        so_hieu: "",
        co_quan_ban_hanh: "",
        tinh_trang_hieu_luc: "",
        terms: terms || [],
      });

      // đánh dấu draft ở object đã tạo
      (created as any).__chatbotDraft = true;

      doc = {
        ...created,
        created_at: new Date().toISOString(),
      };

      msg.fileAttached = doc;
    }

    // ===== 2. CẮT TEXT TRÊN / DƯỚI HIỂN THỊ TRONG CHAT =====
    msg.answerTop =
      block.prefix?.replace(/<\/?answer>/g, "").trim() || "";
    msg.answerBottom =
      block.suffix?.replace(/<\/?answer>/g, "").trim() || "";

    msg.answer = block.cleanedAnswer || fullAnswer;

    // ===== 3. ĐẨY RA VIEWER / DANH SÁCH =====
    if (!silent) {
      if (streaming) {
        this.viewDetailFileService.fileInfor.next(doc);

        // Nếu file đang mở ở canvas thì cập nhật lại tham chiếu
        if (
          this.canvasFile &&
          this.getDocumentKey(this.canvasFile) === this.getDocumentKey(doc)
        ) {
          this.canvasFile = doc;
        }

        this.markActiveFile(doc);
        if (openCanvas && this.isMaximized) {
          this.openCanvas(doc);
        }
      } else {
        // ✅ Stream đã kết thúc:
        // Mở file giống hệt thao tác click vào thẻ DRAFT
        // (router.navigate + FileSearchTemp + setBehavior + openCanvas)
        this.viewFileAttached(doc);
      }
    } else {
      // Rebuild im lặng (load lại lịch sử chat)
      this.viewDetailFileService.fileInfor.next(doc);
      this.markActiveFile(doc);
      if (openCanvas && this.isMaximized) {
        this.openCanvas(doc);
      }
    }
    const currentList = this.chatbotService.listDocument.getValue() || [];
    const docKey = this.getDocumentKey(doc);
    const existed = currentList.some((d) => this.getDocumentKey(d) === docKey);
    if (!existed) {
      this.chatbotService.listDocument.next([doc, ...currentList]);
    }
    return true;
  }

  onOpenDocument(file: any) {
    this.markActiveFile(file); 
    // Đảm bảo đóng mọi modal (nếu có) trước khi mở cái mới / điều hướng
    try { this.modalService?.dismissAll(); } catch {}

    if (this.isMaximized) {
      // ======= FULLSCREEN: mở TAB CANVAS =======
      // 1) Nếu anh đã có CanvasService hay bus nội bộ -> dùng nó:
      // this.canvasService.open({
      //   id: file.id || file.fileId,
      //   title: file.name,
      //   source: 'chatbot'
      // });

      // 2) Nếu đang dùng cơ chế lắng nghe postMessage ở layout chính:
      window.postMessage(
        {
          type: 'OPEN_CANVAS',
          payload: {
            id: file?.id || file?.fileId,
            title: file?.name || file?.title || 'Văn bản',
            // thêm info cần thiết để tab canvas biết phải load nội dung
            from: 'chatbot',
          },
        },
        window.origin || '*'
      );

      // 3) Hoặc nếu anh đã có route dành riêng cho canvas-view:
      // this.router.navigate(['/canvas'], {
      //   queryParams: { id: file?.id || file?.fileId, title: file?.name }
      // });

    } else {
      // ======= KHÔNG FULLSCREEN: mở KHUNG XEM BÊN TRÁI (luồng cũ) =======
      // Trường hợp A: anh có service điều khiển khung trái:
      // this.viewDetailFileService.openInLeftPane(file);

      // Trường hợp B: parent component lắng nghe custom event
      // (dùng nếu không có service sẵn):
      const evt = new CustomEvent('OPEN_DOC_LEFT_PANE', { detail: file });
      window.dispatchEvent(evt);

      // Trường hợp C (fallback): vẫn có thể mở modal cũ (nếu anh muốn):
      // this.types = 'doc'; // nếu app đang dùng field này
      // this.modalService.open(this.viewFileModal, {
      //   size: 'xl',
      //   centered: false,
      //   windowClass: 'cls-view-file'
      // });
    }
  }

  modalOpen(modalSM, type: FormType, size) {
    this.modalService.open(modalSM, {
      centered: true,
      size: size,
    });
    this.types = type;
  }
  transform(value: string): string {
    return value.replace(/<[^>]+>/g, ""); // Xóa tất cả thẻ HTML
  }
  onChatScroll() {
    const el = this.chatContainer.nativeElement;
    const currentScrollTop = el.scrollTop;
    const isScrollingUp = currentScrollTop < this.lastScrollTop;
    const isAtBottom = el.scrollTop + el.clientHeight >= el.scrollHeight - 20;
    
    // Hiển thị nút nếu chưa cuộn hết phần tử chat
    this.showScrollButton =
      el.scrollTop + el.clientHeight < el.scrollHeight - 50;

    // Nếu user đang scroll lên, tắt auto-scroll ngay lập tức
    if (isScrollingUp) {
      this.autoScroll = false;
    } else if (isAtBottom) {
      // Chỉ bật lại auto-scroll khi user scroll về bottom
      this.autoScroll = true;
    }
    
    this.lastScrollTop = currentScrollTop;
  }
  private addBadgeTool(tool: any, labelOverride?: string) {
    const badgeTool = {
      ...tool,
      icon: tool.badgeIcon || tool.icon,
      label: labelOverride ?? tool.label,
    };
    this.toolSelected = [...this.toolSelected, badgeTool];
  }

  selectTool(tool) {
    if (!tool) return;

    // "Tải câu trả lời" => thực thi export ngay và không hiển thị badge
    if (tool.value === 3) {
      this.exportFile("docx");
      return;
    }

    // "Chế độ sáng tạo" => bật cờ và hiển thị badge
    if (tool.value === 4) {
      if (!this.isCreativeMode) {
        this.isCreativeMode = true;
        const exists = this.toolSelected.some((t) => t.value === tool.value);
        if (!exists) {
          this.addBadgeTool(tool, "Sáng tạo");
        }
      }
      return;
    }

    if (tool.value === 5) {
      if (!this.isDraftMode) {
        this.isDraftMode = true;
        const exists = this.toolSelected.some((t) => t.value === 5);
        if (!exists) {
          this.addBadgeTool(tool, "Soạn thảo");
        }
      }
      return;
    }

    // Các công cụ còn lại: tránh thêm trùng lặp
    const exists = this.toolSelected.some((t) => t.value === tool.value);
    if (!exists) {
      this.addBadgeTool(tool);
    }
  }
  removeTool(index) {
    if (index < 0 || index >= this.toolSelected.length) return;
    const removed = this.toolSelected[index];
    this.toolSelected.splice(index, 1);
    if (removed?.value === 4) {
      this.isCreativeMode = false;
    }
    if (removed?.value === 5) {
      this.isDraftMode = false;
    }
  }
  onToolClick(event: MouseEvent, tool: any) {
    if (this.isToolDisabled(tool)) {
      event?.stopPropagation();
      event?.preventDefault();
      return;
    }
    if (tool?.children?.length) {
      event.stopPropagation();
      event.preventDefault();
      // Always open submenu on click (don't toggle) and mark it as click-opened
      // This ensures submenu stays open even if it was previously opened by hover
      this.clearSubmenuCloseTimeout();
      this.activeSubmenu = tool.value;
      this.submenuOpenedByClick = true; // Mark as opened by click - stays open
      return;
    }
    // If clicking other tool options (like "Tải câu trả lời", "Chế độ sáng tạo"), close submenu
    this.activeSubmenu = null;
    this.submenuOpenedByClick = false;
    this.selectTool(tool);
  }

  private scheduleSubmenuClose() {
    // Only schedule close if not opened by click
    if (this.submenuOpenedByClick) {
      return;
    }
    this.clearSubmenuCloseTimeout();
    this.submenuCloseTimeout = setTimeout(() => {
      // Double-check it's still not opened by click before closing
      if (!this.submenuOpenedByClick) {
        this.activeSubmenu = null;
        this.submenuOpenedByClick = false; // Reset flag when closing
      }
    }, 200);
  }

  private clearSubmenuCloseTimeout() {
    if (this.submenuCloseTimeout) {
      clearTimeout(this.submenuCloseTimeout);
      this.submenuCloseTimeout = null;
    }
  }

  onToolHover(tool: any) {
    if (tool?.children?.length) {
      this.clearSubmenuCloseTimeout();
      // If hovering over a different tool, reset the click flag (new hover)
      // But if hovering over the same tool that was click-opened, keep the click-opened state
      if (this.activeSubmenu !== tool.value) {
        this.submenuOpenedByClick = false; // Mark as hover-opened - can timeout
      }
      // Open the submenu
      this.activeSubmenu = tool.value;
    }
  }

  onToolLeave(tool: any) {
    // Only schedule close if submenu was opened by hover (not by click)
    if (tool?.children?.length && !this.submenuOpenedByClick) {
      this.scheduleSubmenuClose();
    }
  }

  selectSubTool(option: any, event?: MouseEvent) {
    event?.stopPropagation();
    if (this.isSubToolSelected(option?.value)) return;
    this.addBadgeTool(option);
    // Keep submenu open after selection - don't close it
  }

  isSubToolSelected(value: number): boolean {
    return this.toolSelected.some((t) => t.value === value);
  }

  onSubmenuEnter(tool: any) {
    if (!tool?.children?.length) return;
    this.clearSubmenuCloseTimeout();
    this.activeSubmenu = tool.value;
    // If hovering over submenu, keep the current state (click-opened stays click-opened)
  }

  onSubmenuLeave() {
    // Only schedule close if submenu was opened by hover (not by click)
    if (!this.submenuOpenedByClick) {
      this.scheduleSubmenuClose();
    }
  }
  editQuestion(index) {
    const userMessages = this.messages.filter((m) => m.role === "user");
    const userMessage = userMessages[(index - 1) / 2].answer; // vì index là 1 3 5 7 ... nên phải xử lý lại các index thàn 0,1,2 ...
    this.userInput = userMessage;
  }

  // Check if a tool should be shown in the popover
  shouldShowTool(tool: any): boolean {
    if (!tool) {
      return false;
    }
    if (tool?.children?.length) {
      return true;
    }
    // Always show the export tool (value 3)
    if (tool.value === 3) {
      return true;
    }
    // Chế độ sáng tạo và các công cụ khác luôn hiển thị
    return true;
  }

  isToolDisabled(tool: any): boolean {
    if (!tool || tool?.children?.length) {
      return false;
    }
    if (tool.value === 3) {
      return false;
    }
    if (tool.value === 4) {
      return this.isCreativeMode;
    }
    if (tool.value === 5) {
      return this.isDraftMode;
    }
    return this.toolSelected.some((t) => t.value === tool.value);
  }
  isAllSelected(): boolean {
    return (
      this.listDocument.length > 0 &&
      this.listDocument.every((doc) => this.isDocumentSelected(doc))
    );
  }
  toggleSelection(index: number) {
    const doc = this.listDocument[index];
    if (!doc) return;
    const key = this.getDocumentKey(doc);
    if (!key) return;

    if (this.selectedDocumentKeys.has(key)) {
      this.selectedDocumentKeys.delete(key);
    } else {
      this.selectedDocumentKeys.add(key);
    }
    this.syncSelectedDocuments();
  }

  toggleAll(event: Event) {
    const checked = (event.target as HTMLInputElement).checked;

    if (checked) {
      this.listDocument.forEach((doc) => {
        const key = this.getDocumentKey(doc);
        if (key) this.selectedDocumentKeys.add(key);
      });
    } else {
      this.selectedDocumentKeys.clear();
    }
    this.syncSelectedDocuments();
  }
  clearDocument(event) {
    event.stopPropagation();
    this.selectedDocumentKeys.clear();
    this.syncSelectedDocuments();
  }
  private getDocumentKey(doc: any): string | null {
    if (!doc) return null;
    const key =
      doc?.es_id ??
      doc?.id ??
      doc?.doc_id ??
      doc?.fileId ??
      null;
    return key !== null && key !== undefined ? String(key) : null;
  }

  private isDocumentSelected(doc: any): boolean {
    const key = this.getDocumentKey(doc);
    return !!key && this.selectedDocumentKeys.has(key);
  }

  private syncSelectedDocuments(): void {
    if (!Array.isArray(this.listDocument) || this.listDocument.length === 0) {
      this.selectedDocuments = [];
      this.selectedFile = [];
      this.selected_save_files = [];
      this.selected_upload_files = [];
      return;
    }

    this.selectedDocuments = this.listDocument.map((doc) =>
      this.isDocumentSelected(doc)
    );

    this.selectedFile = this.listDocument.filter((_, index) => this.selectedDocuments[index]);

    this.selected_save_files = this.selectedFile
      .filter((doc: any) => doc.types === TypeDocument.CSDL)
      .map((doc: any) => (doc.es_id ? doc.es_id : doc.id));

    this.selected_upload_files = this.selectedFile
      .filter((doc: any) => doc.types === TypeDocument.UPLOAD)
      .map((doc: any) => (doc.es_id ? doc.es_id : doc.id));
  }
  feedbackChatbot(message, action) {
    // // console.log("message", message);
    this.chatbotService.feedbackChatbot(message.id, action).subscribe(
      (res) => {
        // Cập nhật lại tin nhắn trong danh sách
        const index = this.messages.findIndex((m) => m.id === message.id);
        if (index !== -1) {
          this.messages[index].feedback = action;
        }
      },
      (error) => {
        this.toast.error("Có lỗi xảy ra khi gửi phản hồi!", "Lỗi", {
          closeButton: true,
          positionClass: "toast-top-right",
          toastClass: "toast ngx-toastr",
        });
      }
    );
  }
  private audio: HTMLAudioElement | null = null;
  isPaused = false;
  textToSpeech(message) {
    if ("speechSynthesis" in window) {
      // Dừng tất cả speech đang chạy
      speechSynthesis.cancel();

      this.idMessageSpeechToText = message.id;
      this.isSpeechToText = true;
      this.isPaused = false;

      // Tạo utterance mới
      const utterance = new SpeechSynthesisUtterance(message.answer);

      // Cấu hình voice
      utterance.lang = "vi-VN"; // Tiếng Việt
      utterance.rate = 1; // Tốc độ đọc
      utterance.pitch = 1; // Cao độ giọng
      utterance.volume = 1; // Âm lượng

      // Xử lý sự kiện kết thúc
      utterance.onend = () => {
        this.isSpeechToText = false;
        this.isPaused = false;
        this.idMessageSpeechToText = null;
      };

      utterance.onerror = (err) => {
        console.error("Lỗi khi phát speech:", err);
        this.isSpeechToText = false;
        this.isPaused = false;
        this.idMessageSpeechToText = null;
      };

      // Bắt đầu đọc
      speechSynthesis.speak(utterance);
    } else {
      this.toast.error("Trình duyệt không hỗ trợ text-to-speech", "Lỗi", {
        closeButton: true,
        positionClass: "toast-top-right",
        toastClass: "toast ngx-toastr",
      });
    }
  }

  pauseTextToSpeech() {
    if (speechSynthesis.speaking && !speechSynthesis.paused) {
      speechSynthesis.pause();
      this.isPaused = true;
      // console.log("Đã tạm dừng speech!");
    }
  }

  resumeTextToSpeech() {
    if (speechSynthesis.paused) {
      speechSynthesis.resume();
      this.isPaused = false;
      // console.log("Đã tiếp tục speech!");
    }
  }

  endTextToSpeech() {
    speechSynthesis.cancel();
    this.isSpeechToText = false;
    this.isPaused = false;
    this.idMessageSpeechToText = null;
  }

  showPopover = false;
  popoverPosition = { top: 0, left: 0 };
  selectedText = "";
  selectedHtml = ""; 
  
private _getSelectionEndRect(range: Range): DOMRect {
  const rects = range.getClientRects();
  if (rects && rects.length > 0) {
    return rects[rects.length - 1]; 
  }
  const tmpSpan = document.createElement('span');
  tmpSpan.textContent = '\u200b';
  range.collapse(false);
  range.insertNode(tmpSpan);
  const rect = tmpSpan.getBoundingClientRect();
  tmpSpan.parentNode?.removeChild(tmpSpan);
  return rect;
}

  onTextSelect() {
    // nếu đang stream thì khỏi bật popup để tránh nhảy
    if (!this.checkDoneAnswer) { this.showPopover = false; return; }
    if (this.disableQuickNote) {
      this.showPopover = false;
      return;
    }
    const container = this.chatContainer?.nativeElement as HTMLElement;
    const sel = window.getSelection();
    const text = sel?.toString();

    if (!container || !text || !sel?.rangeCount) { this.showPopover = false; return; }

    const range = sel.getRangeAt(0);
    const common = range.commonAncestorContainer;
    if (!this._nodeIn(container, common)) { this.showPopover = false; return; }

    const html = this.getSelectedHtmlIn(container);
    if (!html) { this.showPopover = false; return; }
    this.selectedHtml = html;

    // định vị tại cuối vùng chọn (trong KHUNG CUỘN)
    const endRect = this._getSelectionEndRect(range);
    const box = container.getBoundingClientRect();
    const sx = container.scrollLeft || 0;
    const sy = container.scrollTop  || 0;
    const GAP = 10;

    let left = (endRect.left   - box.left) + sx;
    let top  = (endRect.bottom - box.top)  + sy + GAP;

    this.showPopover = true;
    this.popoverPosition = { top, left };

    setTimeout(() => {
      const pop = document.querySelector('.selection-popover') as HTMLElement;
      if (!pop) return;

      const PAD=8, W=pop.offsetWidth||220, H=pop.offsetHeight||44;
      const maxL = container.scrollWidth  - W - PAD;
      const maxT = container.scrollHeight - H - PAD;

      if (left > maxL) left = maxL;
      if (top  > maxT) top  = Math.max(PAD, (endRect.top - box.top) + sy - H - GAP);

      left = Math.max(PAD, Math.min(left, maxL));
      top  = Math.max(PAD, Math.min(top,  maxT));
      this.popoverPosition = { top, left };
    }, 0);
  }

  createNoteFromSelection() {
    if (!this.selectedHtml) return;

    this.noteService.textFromVanBan.next(this.selectedHtml);

    window.dispatchEvent(new CustomEvent('open-notes-tab'));
    this.showPopover = false;
  }

  cancelSelectionPopover() {
    this.showPopover = false;
  }

  viewFileAttached(fileAttached: any) {
    if (!fileAttached) {
      return false;
    }

    this.markActiveFile(fileAttached);

    this.viewDetailFileService.fileInfor.next(fileAttached);

    this.listDocumentService.FileSearchTemp.next(fileAttached);

    const queryParams: any = {
      fileId: fileAttached.id,
      tabs: 'toanvan',
      type: 'searching',
      time: new Date().getTime(),
      fileName: this.transform(fileAttached.name),
      save: true,
      openMode: (fileAttached as any).__chatbotDraft ? 'chatbot-draft' : null,
    };

    if ((fileAttached as any).__chatbotDraft) {
      queryParams.openMode = 'chatbot-draft';
    }

    this.router.navigate([], {
      queryParams,
      queryParamsHandling: 'merge',
    });

    this.listDocumentService.setBehavior(ShowContent.Search);

    // Nếu đang fullscreen thì mở luôn split view / canvas bên phải
    if (this.isMaximized) {
      this.openCanvas(fileAttached);
    }

    return true;
  }

  // Xuất hội thoại theo định dạng md/html/docx; mặc định là docx
  exportFile(format: "md" | "html" | "docx" = "docx") {
    try {
      // Tạo chuỗi thời gian YYYYMMDD-HHMM để đặt tên file
      const now = new Date();
      const y = now.getFullYear();
      const m = String(now.getMonth() + 1).padStart(2, "0");
      const d = String(now.getDate()).padStart(2, "0");
      const hh = String(now.getHours()).padStart(2, "0");
      const mm = String(now.getMinutes()).padStart(2, "0");

      // Lấy tên workspace/hội thoại làm base name
      const wsName =
        (this.selectedConversation?.name ?? this.workSpaceName ?? "chatbot")
          .toString()
          .trim() || "chatbot";
      const base = `${wsName}-${y}${m}${d}-${hh}${mm}`;

      // Nhánh xuất markdown hoặc HTML trực tiếp bằng Blob
      if (["md", "html"].includes(format)) {
        let content = "";
        let mime = "text/plain;charset=utf-8";
        let filename = `${base}.md`;

        // Chuyển messages -> nội dung theo định dạng tương ứng
        switch (format) {
          case "md":
            content = this._buildMarkdownTranscript(
              this.messages ?? [],
              wsName
            );
            mime = "text/markdown;charset=utf-8";
            filename = `${base}.md`;
            break;
          case "html":
            content = this._buildHtmlTranscript(this.messages ?? [], wsName);
            mime = "text/html;charset=utf-8";
            filename = `${base}.html`;
            break;
        }

        // Tạo Blob và trigger download thủ công bằng thẻ <a> tạm
        const blob = new Blob([content], { type: mime });
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = filename;
        a.style.display = "none";
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        // Thông báo thành công
        this.toast?.success("Đã xuất hội thoại", "Thành công", {
          closeButton: true,
          positionClass: "toast-top-right",
          toastClass: "toast ngx-toastr",
        });
        return;
      }

      // Nhánh xuất DOCX: tạo Blob async rồi tải về
      if (format === "docx") {
        if (!Array.isArray(this.messages) || this.messages.length === 0) {
          this.toast?.info("Hãy bắt đầu hội thoại trước khi xuất dữ liệu", "Thông báo", {
            closeButton: true,
            positionClass: "toast-top-right",
            toastClass: "toast ngx-toastr",
          });
          return;
        }

        this._buildDocxBlob(this.messages ?? [], wsName)
          .then((blob) => {
            const url = URL.createObjectURL(blob);
            const a = document.createElement("a");
            a.href = url;
            a.download = `${base}.docx`;
            a.style.display = "none";
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            this.toast?.success("Đã xuất hội thoại", "Thành công", {
              closeButton: true,
              positionClass: "toast-top-right",
              toastClass: "toast ngx-toastr",
            });
          })
          .catch((e) => {
            console.error(e);
            this.toast?.error("Không thể xuất hội thoại", "Lỗi", {
              closeButton: true,
              positionClass: "toast-top-right",
              toastClass: "toast ngx-toastr",
            });
          });
        return;
      }
    } catch (e) {
      // Bắt lỗi tổng thể quá trình export
      console.error("Export failed:", e);
      this.toast?.error("Không thể xuất hội thoại", "Lỗi", {
        closeButton: true,
        positionClass: "toast-top-right",
        toastClass: "toast ngx-toastr",
      });
    }
  }

  // Xây dựng file HTML hoàn chỉnh (head + body) từ messages
  private _buildHtmlTranscript(messages: any[], title: string): string {
    // Hàm escape ký tự HTML cơ bản cho <title>...
    const esc = (s: string) =>
      (s || "").replace(
        /[&<>"]/g,
        (c) =>
          (({ "&": "&amp;", "<": "&lt;", ">": "&gt;", '"': "&quot;" } as any)[
            c
          ])
      );
    // Khung HTML + style in trang
    const head = `<!doctype html><html><head><meta charset="utf-8"><title>${esc(
      title
    )}</title>
  <style>
    body{font-family:system-ui,-apple-system,Segoe UI,Roboto,Arial,sans-serif;max-width:1000px;margin:24px auto;padding:0 16px;line-height:1.6;color:#111}
    h1{margin:0 0 8px 0}
    .meta{font-size:12px;color:#666;margin-bottom:12px}
    table.turns{width:100%;border-collapse:collapse;table-layout:fixed}
    .who{width:140px;vertical-align:top;font-weight:700;color:#ea580c; /* cam */ padding:8px 10px}
    .content{vertical-align:top;padding:8px 12px;border-left:1px solid #e5e7eb}
    tr + tr td{border-top:1px solid #e5e7eb}
    .content p{margin:0 0 8px 0}
    .content ul,.content ol{margin:8px 0 8px 20px}
    a{color:#2563eb;text-decoration:underline}
  </style></head><body>`;
    // Thân HTML (bảng hội thoại)
    const inner = this._buildHtmlInnerTranscript(messages, title);
    return head + inner + "</body></html>";
  }

  // Xây phần thân HTML: render từng lượt hội thoại thành bảng
  private _buildHtmlInnerTranscript(messages: any[], title: string): string {
    // Escape chuỗi cho các vị trí cần thiết
    const esc = (s: string) =>
      (s || "").replace(
        /[&<>"]/g,
        (c) =>
          (({ "&": "&amp;", "<": "&lt;", ">": "&gt;", '"': "&quot;" } as any)[
            c
          ])
      );

    // Style dành cho nội dung in (A4, margin)
    const style = `
  <style>
    @page { size: A4; margin: 12mm; }
    body{font-family:system-ui,-apple-system,Segoe UI,Roboto,Arial,sans-serif;line-height:1.6;color:#111}
    h1{margin:0 0 8px 0}
    .meta{font-size:12px;color:#666;margin-bottom:12px}
    table.turns{width:100%;border-collapse:collapse;table-layout:fixed}
    td.who{width:120px;vertical-align:top;font-weight:700;padding:8px 10px}
    td.who.user{color:#111}           /* Bạn: */
    td.who.assistant{color:#ea580c}   /* AI pháp luật: (cam) */
    td.content{vertical-align:top;padding:8px 12px;border-left:1px solid #e5e7eb}
    tr + tr td{border-top:1px solid #e5e7eb}
    .content p{margin:0 0 8px 0}
    .content ul,.content ol{margin:8px 0 8px 20px}
    a{color:#2563eb;text-decoration:underline}
  </style>`;

    const rows: string[] = [];
    for (const msg of messages || []) {
      // Nhãn người nói
      const who = msg.role === "user" ? "Bạn:" : "AI pháp luật:";
      const whoClass = msg.role === "user" ? "user" : "assistant";

      // Làm sạch nội dung cho từng vai trò (assistant có thể có thẻ <answer>)
      const raw =
        msg.role === "assistant"
          ? String(msg.answer || "").replace(/<\/?answer>/g, "")
          : this._htmlToText(String(msg.answer || ""));

      // Chuyển markdown cơ bản sang HTML (list/paragraph/link)
      const html = this._mdToHtmlBasic(raw);

      // Thêm một hàng vào bảng hội thoại
      rows.push(
        `<tr>
        <td class="who ${whoClass}">${who}</td>
        <td class="content">${html}</td>
      </tr>`
      );
    }

    // Header + meta + bảng nội dung
    return `
    ${style}
    <h1>${esc(title)}</h1>
    <div class="meta">Exported: ${esc(new Date().toLocaleString())}</div>
    <table class="turns"><tbody>${rows.join("")}</tbody></table>
  `;
  }

  // Xây dựng Blob DOCX từ messages (sử dụng docx library)
  private async _buildDocxBlob(messages: any[], title: string): Promise<Blob> {
    // console.log("msg for docx", messages);
    const children: any[] = [
      // Tiêu đề tài liệu và dòng meta thời gian
      new Paragraph({
        text: title || "Hội thoại",
        // heading: HeadingLevel.TITLE,
        heading: HeadingLevel.HEADING_3,
        alignment: AlignmentType.LEFT,
      }),
      // new Paragraph({
      //   children: [
      //     new TextRun({
      //       text: `Exported: ${new Date().toLocaleString()}`,
      //       italics: true,
      //       color: "666666",
      //     }),
      //   ],
      //   spacing: { after: 200 },
      // }),
    ];

    // Bộ style viền rỗng để bảng/ô không có đường viền
    const NONE = { style: BorderStyle.NONE, size: 0, color: "FFFFFF" } as const;
    const noBorders = {
      top: NONE,
      bottom: NONE,
      left: NONE,
      right: NONE,
      insideHorizontal: NONE,
      insideVertical: NONE,
    };

    // Phân tích bold / italic trong text
    const makeTextRuns = (text: string): TextRun[] => {
      const parts: TextRun[] = [];

      // Tách theo group: [ ... ] hoặc phần còn lại
      const regex = /(\[.+?\])/g;

      const segments = text.split(regex); // giữ nguyên text ngoài ngoặc

      for (const seg of segments) {
        if (!seg) continue;

        // Nếu là dạng [ ... ] => màu xanh 1E40AF
        if (/^\[.+?\]$/.test(seg)) {
          parts.push(
            new TextRun({
              text: seg.slice(1, -1), // bỏ dấu []
              color: "1E40AF",
            })
          );
          continue;
        }

        // Các style khác: bold, italic
        const innerRegex =
          /(\*\*\*.+?\*\*\*|\*\*.+?\*\*|\*.+?\*|[^*]+)/g;
        let match;
        while ((match = innerRegex.exec(seg))) {
          const token = match[0];

          if (/^\*\*\*(.+)\*\*\*$/.test(token))
            parts.push(new TextRun({ text: token.slice(3, -3), bold: true, italics: true }));
          else if (/^\*\*(.+)\*\*$/.test(token))
            parts.push(new TextRun({ text: token.slice(2, -2), bold: true }));
          else if (/^\*(.+)\*$/.test(token))
            parts.push(new TextRun({ text: token.slice(1, -1), italics: true }));
          else
            parts.push(new TextRun({ text: token }));
        }
      }
      return parts.length ? parts : [new TextRun(" ")];
    };

    // Phân tích dòng markdown có indent (list lồng nhau)
    const parseMarkdownLine = (line: string) => {
      const raw = line ?? "";
      const indentSpaces = raw.match(/^\s*/)?.[0]?.length || 0;
      const level = Math.floor(indentSpaces / 2); // mỗi 2 space = 1 cấp
      const t = raw.trim();

      if (!t) return { type: "empty", text: " ", level };

      const heading = /^(#{1,6})\s+(.+)$/.exec(t);
      if (heading)
        return { type: "heading", level: heading[1].length, text: heading[2] };

      // const ordered = /^(\d+)[.)]\s+(.+)$/.exec(t);
      // if (ordered)
      //   return { type: "ordered", text: ordered[2], level };

      const bullet = /^([-*•])\s+(.+)$/.exec(t);
      if (bullet)
        return { type: "bullet", text: bullet[2], level };

      if (/^-{3,}$/.test(t)) 
        return { type: "hr", text: "", level };

      return { type: "normal", text: t, level };
    };

    // Tạo Paragraph tương ứng
    const makePara = (line: string) => {
      const parsed = parseMarkdownLine(line);

      switch (parsed.type) {
        case "heading":
          return new Paragraph({
            // children: makeTextRuns(parsed.text),
            children: [
              new TextRun({
                text: parsed.text,
                bold: true,
                color: "000000", // ép màu đen
              }),
            ],
            heading: HeadingLevel[`HEADING_${Math.min(parsed.level, 6)}`],
            spacing: { after: 120 },
          });

        // case "ordered":
        //   return new Paragraph({
        //     children: makeTextRuns(parsed.text),
        //     numbering: { reference: "ordered-list", level: Math.min(parsed.level, 5) },
        //     spacing: { after: 40 },
        //   });

        case "bullet":
          return new Paragraph({
            children: makeTextRuns(parsed.text),
            bullet: { level: Math.min(parsed.level, 5) },
            spacing: { after: 40 },
          });

        case "hr":
          return new Paragraph({
            children: [],
            border: {
              bottom: {
                color: "999999",
                size: 5,     // độ dày
                style: BorderStyle.SINGLE,
              },
            },
            // spacing: { before: 200, after: 200 },
          });

        default: {
          // Nếu bắt đầu bằng số (ordered-like) và có indent => thụt lề
          const isNumbered = /^\d+[.)]\s+/.test(parsed.text);
          if (isNumbered && parsed.level > 0) {
            return new Paragraph({
              children: makeTextRuns(parsed.text),
              indent: { left: 540 * (parsed.level + 1) }, // 0.5 inch mỗi cấp
              spacing: { after: 40 },
            });
          }

          // Mặc định
          return new Paragraph({
            children: makeTextRuns(parsed.text),
            spacing: { after: 60 },
          });
        }
      }
    };

    const rows: any[] = [];

    // Xử lý markdown bảng
    const isTableRow = (line: string) => /^\s*\|.*\|\s*$/.test(line);
    const isSeparatorRow = (line: string) => /^\s*\|?(?:\s*:?-+:?\s*\|)+\s*$/.test(line);

    // Parse 1 nhóm bảng
    const parseMarkdownTable = (lines: string[], startIdx: number) => {
      const rows: string[][] = [];
      let i = startIdx;

      // dòng tiêu đề
      if (!isTableRow(lines[i])) return null;
      const header = lines[i].split("|").slice(1, -1).map(c => c.trim());
      rows.push(header);
      i++;

      // dòng phân tách |---|
      if (!isSeparatorRow(lines[i])) return null;
      i++;

      // các dòng dữ liệu
      while (i < lines.length && isTableRow(lines[i])) {
        const cols = lines[i].split("|").slice(1, -1).map(c => c.trim());
        rows.push(cols);
        i++;
      }

      return {
        table: rows,
        nextIndex: i
      };
    };

    // Convert rows[][] => bảng DOCX
    const makeDocxTable = (rows: string[][]) => {
      return new Table({
        width: { size: 100, type: WidthType.PERCENTAGE },
        borders: {
          top: { style: BorderStyle.SINGLE, size: 1, color: "000000" },
          bottom: { style: BorderStyle.SINGLE, size: 1, color: "000000" },
          left: { style: BorderStyle.SINGLE, size: 1, color: "000000" },
          right: { style: BorderStyle.SINGLE, size: 1, color: "000000" },
          insideHorizontal: { style: BorderStyle.SINGLE, size: 1, color: "000000" },
          insideVertical: { style: BorderStyle.SINGLE, size: 1, color: "000000" },
        },
        rows: rows.map((cols, rowIndex) =>
          new TableRow({
            children: cols.map((col, colIndex) =>
              new TableCell({
                children: [
                  new Paragraph({
                    children: rowIndex === 0
                      ? [
                          new TextRun({
                            text: col,
                            bold: true,
                            color: "000000",
                          }),
                        ]
                      : makeTextRuns(col),
                    spacing: { after: 80 },
                  })
                ]
              })
            )
          })
        ),
      });
    };


    // Duyệt message -> dựng bảng 2 cột (người nói | nội dung)
    for (const msg of messages || []) {
      const who = msg.role === "user" ? "Bạn:" : "Chatbot:";

      // Làm sạch nội dung theo vai trò
      let raw =
        msg.role === "assistant"
          ? String(msg.answer || "").replace(/<\/?answer>/g, "")
          : this._htmlToText(String(msg.answer || ""));

      // Xóa mọi cụm (legal: ... )
      raw = raw.replace(/\(legal:[^)]*\)/g, "");

      const plain = this._mdLinksToPlain(raw);
      const lines = plain.split(/\r?\n/);
      // const contentParas = lines.length
      //   ? lines.map(makePara)
      //   : [new Paragraph({ text: " " })];
      const contentParas: any[] = [];

      let i = 0;
      while (i < lines.length) {
        const line = lines[i];

        // Nếu dòng này là bảng => parse toàn bộ block bảng
        if (isTableRow(line)) {
          const parsed = parseMarkdownTable(lines, i);
          if (parsed) {
            contentParas.push(makeDocxTable(parsed.table));
            i = parsed.nextIndex;
            continue;
          }
        }

        // Nếu không phải bảng => paragraph bình thường
        contentParas.push(makePara(line));
        i++;
      }

      if (contentParas.length === 0) {
        contentParas.push(new Paragraph({ text: " " }));
      }

      // Ô bên trái: nhãn người nói, nền cam nhạt, không viền
      const whoCell = new TableCell({
        width: { size: 18, type: WidthType.PERCENTAGE },
        verticalAlign: VerticalAlign.TOP,
        margins: { top: 120, bottom: 80, left: 160, right: 120 }, // twips
        shading: { fill: "FFF7ED" }, // cam nhạt
        borders: noBorders,
        children: [
          new Paragraph({
            children: [new TextRun({ text: who, bold: true, color: "EA580C" })], // chữ cam đậm
          }),
        ],
      });

      // Ô bên phải: nội dung hội thoại, không viền

      // Thêm các đoạn phụ nếu có selection_text hoặc selected_files (với user)
      const extraParas: Paragraph[] = [];
      if (msg.role === "user") {
        // Nếu có selection_text
        if (msg.selection_text) {
          // Tách dòng để giữ định dạng trong selection_text
          const lines = msg.selection_text.split(/\n/);

          for (const line of lines) {
            if (line.trim()) {
              if (line === lines[0]) {
                extraParas.push(new Paragraph({
                  children: [new TextRun({ text: `⤷ ${line}`, bold: true })],
                  spacing: { before: 120, after: 60 },
                }));
                continue;
              }
              extraParas.push(new Paragraph({
                children: [new TextRun({ text: `${line}`, bold: true })],
                spacing: { before: 120, after: 60 },
              }));
            }
          }
        }
  
        // Nếu có selected_files (save_files + upload_files)
        const saveFiles = msg.selected_files?.save_files || [];
        const uploadFiles = msg.selected_files?.upload_files || [];
        const allFiles = [...saveFiles, ...uploadFiles];
  
        if (allFiles.length > 0) {
          extraParas.push(
            new Paragraph({
              children: [new TextRun({ text: "Tài liệu đính kèm:", })],
              spacing: { before: 120, after: 60 },
            })
          );
  
          for (const f of allFiles) {
            extraParas.push(
              new Paragraph({
                children: [
                  new TextRun({ text: `- ${f.name}`, color: "1E40AF" }), // xanh đậm nhẹ
                ],
                spacing: { after: 40 },
              })
            );
          }
        }
      }

      const contentCell = new TableCell({
        width: { size: 82, type: WidthType.PERCENTAGE },
        verticalAlign: VerticalAlign.TOP,
        margins: { top: 120, bottom: 80, left: 160, right: 160 },
        borders: noBorders,
        children: [...contentParas, ...extraParas],
      });

      // Thêm một hàng (không khóa cantSplit để cho phép tràn sang trang)
      rows.push(new TableRow({ children: [whoCell, contentCell] }));
    }

    // Định nghĩa nhiều cấp numbering và gói thành tài liệu DOCX rồi chuyển thành Blob
    const doc = new DocxDocument({
      numbering: {
        config: [
          {
            reference: "ordered-list",
            levels: Array.from({ length: 6 }, (_, i) => ({
              level: i,
              format: "decimal",
              text: `%${i + 1}.`,
              alignment: AlignmentType.LEFT,
              style: { paragraph: { indent: { left: 720 * (i + 1) } } }, // thụt 0.5 inch mỗi cấp
            })),
          },
        ],
      },
      sections: [
        {
          properties: {},
          children: [
            ...children,
            new Table({
              width: { size: 100, type: WidthType.PERCENTAGE },
              borders: noBorders,
              rows,
            }),
          ],
        },
      ],
    });

    return Packer.toBlob(doc);
  }

  // Kết xuất Markdown: bảng 2 cột (Người nói | Nội dung)
  private _buildMarkdownTranscript(messages: any[], title: string): string {
    const escPipe = (s: string) => (s || "").replace(/\|/g, "\\|");
    const header = `# ${this._escMdInline(
      title
    )}\n_Exported: ${new Date().toLocaleString()}_\n\n`;
    const lines: string[] = [header, "| Người nói | Nội dung |", "|---|---|"];
    for (const msg of messages || []) {
      const who = msg.role === "user" ? "Bạn:" : "Chatbot:";
      const raw =
        msg.role === "assistant"
          ? String(msg.answer || "").replace(/<\/?answer>/g, "")
          : this._htmlToText(String(msg.answer || ""));
      // Thay xuống dòng bằng <br> để hiển thị gọn trong bảng Markdown
      const cell = escPipe((raw || "").replace(/\r?\n/g, "<br>"));
      lines.push(`| ${who} | ${cell} |`);
    }
    return lines.join("\n");
  }

  // Chuyển markdown cơ bản -> HTML đơn giản (link/list/paragraph)
  private _mdToHtmlBasic(md: string): string {
    if (!md) return "";
    // Link markdown -> thẻ <a>
    let s = md.replace(
      /\[(.+?)\]\((https?:\/\/[^\s)]+)\)/g,
      '<a href="$2" target="_blank" rel="noopener">$1</a>'
    );

    // Duyệt từng dòng để render list/paragraph
    const lines = s.split(/\r?\n/);
    const out: string[] = [];
    let inUl = false,
      inOl = false;

    // Đóng các khối danh sách đang mở
    const flush = () => {
      if (inUl) {
        out.push("</ul>");
        inUl = false;
      }
      if (inOl) {
        out.push("</ol>");
        inOl = false;
      }
    };

    for (let line of lines) {
      const ul = /^\s*[-*•]\s+(.+)$/.exec(line);
      const ol = /^\s*\d+[.)]\s+(.+)$/.exec(line);

      if (ul) {
        if (!inUl) {
          flush();
          out.push("<ul>");
          inUl = true;
        }
        out.push(`<li>${ul[1]}</li>`);
        continue;
      }

      if (ol) {
        if (!inOl) {
          flush();
          out.push("<ol>");
          inOl = true;
        }
        out.push(`<li>${ol[1]}</li>`);
        continue;
      }

      // Không phải list -> đóng list nếu đang mở, rồi render paragraph/br
      flush();
      if (line.trim() === "") out.push("<br/>");
      else out.push(`<p>${line}</p>`);
    }

    // Đảm bảo đóng khối nếu còn
    flush();
    return out.join("");
  }

  // Chuyển HTML sang text thuần (dùng DOM để strip tag)
  private _htmlToText(html: string): string {
    const div = document.createElement("div");
    div.innerHTML = html || "";
    return (div.textContent || div.innerText || "").trim();
  }

  // Đổi markdown link [text](url) -> "text (url)" cho DOCX
  private _mdLinksToPlain(md: string): string {
    if (!md) return "";
    return md.replace(/\[(.+?)\]\((https?:\/\/[^\s)]+)\)/g, "$1 ($2)");
  }

  // Escape inline cho markdown title
  private _escMdInline(s: string): string {
    return (s || "").replace(/([\\`*_{}\[\]()#+\-.!|>])/g, "\\$1");
  }
  toggleMobileSidebar(): void {
    this.isMobileSidebarOpen = !this.isMobileSidebarOpen;
    
    // Prevent body scroll when sidebar is open
    if (this.isMobileSidebarOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
  }

  private setupResizeListener(): void {
    window.addEventListener('resize', this.handleResize);
  }

  private handleResize = (): void => {
    // Đóng sidebar khi chuyển sang màn hình lớn hơn 768px
    if (window.innerWidth > 768 && this.isMobileSidebarOpen) {
      this.isMobileSidebarOpen = false;
      document.body.style.overflow = '';
    }
  }
  ngOnDestroy() {
    this.unSubAll.next(null);
    this.unSubAll.complete();
    this.chatbotService.textFormVanBan.next(null);
    window.removeEventListener('resize', this.handleResize);
    this.clearSubmenuCloseTimeout();
  }
}
