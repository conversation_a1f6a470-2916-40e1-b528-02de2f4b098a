import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Params } from "@angular/router";
import { InterceptorSkipHeader } from "@core/components/loading/loading.interceptor";
import { environment } from "environments/environment";
import { BehaviorSubject } from "rxjs";

@Injectable({
  providedIn: "root",
})
export class ViewDetailFileService {
  // public fileName: BehaviorSubject<string> = new BehaviorSubject<string>("");
  public fileInfor: BehaviorSubject<object> = new BehaviorSubject<object>(null);

  public clauseId: BehaviorSubject<string> = new BehaviorSubject<string>(""); // là id của điều từ chatbot
  public clauseId2: BehaviorSubject<string> = new BehaviorSubject<string>(""); // là id của điều từ danh sáhc văn bản
  public isSaveFileFromSearch: BehaviorSubject<boolean> =
    new BehaviorSubject<boolean>(false);
  public listDieuKhoanSearch: BehaviorSubject<any[]> = new BehaviorSubject<
    any[]
  >([]);
  public graphData: BehaviorSubject<any> = new BehaviorSubject<any>(null);
  constructor(private _http: HttpClient) {}
  getDetailFile(fileId, doc_type) {
    const params: Params = {
      document_id: fileId,
      doc_type: doc_type,
    };
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    return this._http.get<any>(`${environment.apiUrl}/legal_search/by_query`, {
      params,
      headers,
    });
  }
  getDetailFileFromSearch(fileId, doc_type, from_luoc_do) {
    const params: Params = {
      document_id: fileId,
      from_search: 1, // đối với các file trừ file người dùng upload lên
      from_luoc_do: from_luoc_do, // đối với các file được xem từ lược đồ và văn bản liên quan
      doc_type,
    };
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    return this._http.get<any>(`${environment.apiUrl}/legal_search/by_query`, {
      params,
      headers,
    });
  }
  getLuocDo(fileId) {
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    return this._http.get<any>(
      `${environment.apiUrl}/ocr/documents/${fileId}/luoc_do`,
      { headers }
    );
  }
  getDoThi(body: any) {
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    return this._http.post<any>(
      `${environment.apiUrl}/ocr/documents/do_thi`,
      body,
      { headers }
    );
  }
  getLuocDoFileSearch(fileId) {
    return this._http.get<any>(
      `${environment.apiUrl}/ocr/documents/${fileId}/luoc_do`
    );
  }
  editTongQuan(fileId, body) {
    return this._http.put(
      `${environment.apiUrl}/legal_search/by_query/${fileId}`,
      body
    );
  }
  deleteRelateFile(fileId) {
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    return this._http.delete(
      `${environment.apiUrl}/ocr/document-related/${fileId}`,
      { headers }
    );
  }
  addFileBySearch(body) {
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    return this._http.post<any>(
      `${environment.apiUrl}/ocr/documents/add_search_document`,
      body,
      { headers }
    );
  }
  summarizeDocument(idDocument) {
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    return this._http.get<any>(
      `${environment.apiUrl}/summarize/?document=${idDocument}`,
      { headers }
    );
  }
  saveSumarizeDocument(body) {
    return this._http.post<any>(`${environment.apiUrl}/summarize/`, body);
  }

  getLawClauseContent(clauseId: string, docId: string) {
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    const params: Params = {
      clause_id: clauseId,
      doc_id: docId,
    };
    return this._http.get<any>(
      `${environment.apiUrl}/ocr/documents/get_law_clause_content`,
      { params, headers }
    );
  }
  
}
