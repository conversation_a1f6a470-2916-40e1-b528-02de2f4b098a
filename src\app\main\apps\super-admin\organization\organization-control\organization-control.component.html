<div class="modal-body organization-modal" tabindex="0" ngbAutofocus>
  <div class="form-group">
    <label
      for="basicTextarea"
      class="organization-modal__title w-100 align-items-center d-flex justify-content-between"
      >{{ title }}
      <div class="">
        <button
          class="btn btn-sm ml-auto p-0"
          (click)="modal.dismiss('Cross click')"
        >
          <img src="assets/images/icons/x.svg" alt="x" />
        </button></div
    ></label>
  </div>
  <div class="container-xxl p-0">
    <form [formGroup]="organizationForm" (ngSubmit)="submitSaveOrganization()">
      <div class="organization-form">
        <div class="organization-form__grid">
          <div class="organization-form__item col-md-12 mb-1">
            <div class="organization-form__label">
              <label
                for="orgName"
                class="organization-form__label--title form-label"
                >{{ role == "SUPER_ADMIN" ? "Tên tổ chức" : "Tên phòng ban" }}
                <span class="text-danger">*</span>
              </label>
              <p>Tên tổ chức hiển thị trên ứng dụng</p>
            </div>
            <span>
              <input
                id="orgName"
                type="text"
                class="organization-form__input form-control"
                formControlName="name"
                placeholder="Nhập"
                appFormControlValidation
              />
            </span>
          </div>
          <div
            class="organization-form__item col-12 mb-2"
            *ngIf="role == 'SUPER_ADMIN'"
          >
            <div class="organization-form__label">
              <label>Trực thuộc tổ chức</label>
              <p>Tên tổ chức trực tiếp quản lý tổ chức này</p>
            </div>
            <span>
              <!-- <ng-select
                class="organization-form__input"
                [items]="listOrganization"
                placeholder="Chọn"
                bindLabel="name"
                bindValue="id"
                formControlName="parent_organization"
              >
              </ng-select> -->

              <fieldset class="form-group">
                <div class="input-group custom-org-picker cursor-pointer" (click)="onSelectOrganizationClick($event)">
                  <input
                    class="form-control cursor-pointer bg-transparent organization-form__input"
                    placeholder="Chọn"
                    [value]="selectedOrganization?.name"
                    readonly
                  />
                </div>
              </fieldset>
            </span>
          </div>
          <div class="organization-form__item col-12 mb-1">
            <div class="organization-form__label">
              <label class="form-label">Họ và tên người đại diện</label>
            </div>
            <span>
              <input
                id="description"
                type="text"
                class="organization-form__input form-control"
                formControlName="represent_people"
                placeholder="Nhập"
                appFormControlValidation
              />
            </span>
          </div>
          <div class="organization-form__item col-md-12 mb-1">
            <div class="organization-form__label">
              <label for="email" class="form-label">Email</label>
            </div>
            <span>
              <input
                id="email"
                type="email"
                class="organization-form__input form-control"
                formControlName="email"
                appFormControlValidation
                placeholder="Nhập"
              />
            </span>
          </div>
          <div class="organization-form__item col-md-12 mb-1">
            <div class="organization-form__label">
              <label for="phoneNumber" class="form-label">Số điện thoại</label>
            </div>
            <span>
              <input
                id="phoneNumber"
                type="text"
                class="organization-form__input form-control"
                formControlName="phone"
                appFormControlValidation="Số điện thoại phải bắt đầu bằng số 0 và có 10 chữ số"
                placeholder="Nhập"
              />
            </span>
          </div>
          <div class="organization-form__item col-md-12 mb-1">
            <div class="organization-form__label">
              <label for="address" class="form-label">Địa chỉ</label>
            </div>
            <span>
              <input
                id="address"
                type="text"
                class="organization-form__input form-control"
                formControlName="address"
                placeholder="Nhập"
                appFormControlValidation
              />
            </span>
          </div>
          <div class="organization-form__item col-md-12 mb-1">
            <div class="organization-form__label">
              <label for="appName" class="form-label">
                Tên ứng dụng
                <span class="text-danger">*</span>
              </label>
            </div>
            <span>
              <input
                id="appName"
                type="text"
                class="organization-form__input form-control"
                formControlName="app_name"
                [readonly]="hasSelectedParent"
                placeholder="Nhập"
                appFormControlValidation
              />
            </span>
          </div>
        </div>
        <span class="organization-form__separator"></span>
        <!-- Logo upload field -->
        <div
          class="organization-form__item organization-form__theme col-md-12 mb-1"
        >
          <div class="organization-form__label">
            <label for="logo" class="form-label">
              {{ role == "SUPER_ADMIN" ? "Logo tổ chức" : "Logo phòng ban" }}
            </label>
            <p>
              Cập nhật logo hiển thị trên ứng dụng.<br />
              Tỉ lệ 1:1
            </p>
          </div>

          <div class="organization-logo__container">
            <div class="organization-image__preview organization-logo__preview">
              <img
                *ngIf="logoPreviewUrl"
                [src]="logoPreviewUrl"
                alt="Logo preview"
                class="organization-logo__image"
              />
            </div>
            <div class="organization-logo__action" *ngIf="!hasSelectedParent">
              <div class="organization-logo__action--container">
                <button
                  type="button"
                  class="btn btn-organization-logo"
                  (click)="logoInput.click()"
                >
                  {{ logoPreviewUrl ? "Thay thế logo" : "Chọn logo" }}
                </button>
                <a
                  *ngIf="logoPreviewUrl && !isDefaultLogo"
                  href="javascript:void(0)"
                  class="organization-logo__reset-link"
                  (click)="restoreDefaultImage('assets/images/logo/CMC.png', 'logo')"
                >
                  Khôi phục logo mặc định
                </a>
              </div>
              <input
                #logoInput
                id="logo"
                type="file"
                accept="image/*"
                (change)="onSelectImage($event, 'logo')"
                hidden
              />
            </div>
          </div>
        </div>
        <span class="organization-form__separator"></span>
        <!-- Background image upload field -->
        <div
          class="organization-form__item organization-form__theme col-md-12 mb-1"
        >
          <div class="organization-form__label">
            <label for="backgroundImage" class="form-label">
              {{
                role == "SUPER_ADMIN"
                  ? "Hình nền tổ chức"
                  : "Hình nền phòng ban"
              }}
            </label>
            <p>
              Cập nhật hình nền hiển thị ở màn đăng nhập.<br />
              Tỉ lệ 16:9
            </p>
          </div>

          <div class="organization-background__container">
            <div
              class="organization-image__preview organization-background__preview"
            >
              <img
                *ngIf="backgroundPreviewUrl"
                [src]="backgroundPreviewUrl"
                alt="Logo preview"
                class="organization-logo__image"
              />
            </div>
            <div
              class="organization-background__action"
              *ngIf="!hasSelectedParent"
            >
              <div class="organization-background__action--container">
                <button
                  type="button"
                  class="btn btn-organization-background"
                  (click)="backgroundInput.click()"
                >
                  {{
                    backgroundPreviewUrl ? "Thay thế hình nền" : "Chọn hình nền"
                  }}
                </button>
                <a
                  *ngIf="backgroundPreviewUrl && !isDefaultBackground"
                  href="javascript:void(0)"
                  class="organization-logo__reset-link"
                  (click)="restoreDefaultImage('assets/images/illustration/loginBG.png', 'background')"
                >
                  Khôi phục hình nền mặc định
                </a>
              </div>
              <input
                #backgroundInput
                id="backgroundImage"
                type="file"
                accept="image/*"
                (change)="onSelectImage($event, 'background')"
                hidden
              />
            </div>
          </div>
        </div>
        <span *ngIf="!hasSelectedParent" class="organization-form__separator"></span>
        <!-- Color picker field -->
        <div
          *ngIf="!hasSelectedParent"
          class="organization-form__item organization-form__theme col-md-12 mb-1"
        >
          <div class="organization-form__label">
            <label for="primaryColor" class="form-label">Màu chủ đạo</label>
            <p>Chọn màu sắc chủ đạo hiển thị trên ứng dụng.</p>
          </div>
          <div class="color-picker-group">
            <input
              id="primaryColor"
              type="color"
              class="form-control organization-form__color-code"
              formControlName="color_code"
              (input)="updateColor($event)"
            />
            <a
              *ngIf="!isDefaultColor"
              href="javascript:void(0)"
              class="organization-logo__reset-link"
              (click)="restoreDefaultColor()"
            >
              Khôi phục màu chủ đạo mặc định
            </a>
          </div>
        </div>
      </div>
      <div class="w-100 justify-content-end d-flex">
        <button
          type="button"
          rippleEffect
          class="btn btn-secondary mr-1"
          (click)="modal.close('Cross click'); onCancel()"
        >
          Huỷ
        </button>
        <button
          rippleEffect
          type="submit"
          class="btn btn-primary-theme"
        >
          {{
            type === FormType.Create
              ? role === "SUPER_ADMIN"
                ? "Thêm tổ chức"
                : "Thêm phòng ban"
              : "Xác nhận"
          }}
        </button>
      </div>
    </form>
  </div>
</div>


<!-- Modal chọn tổ chức -->
<ng-template #modalSelectOrg let-modal>
  <div class="modal-header">
    <h4 class="modal-title">Chọn tổ chức</h4>
		<button type="button" class="close" (click)="modal.dismiss('Cross click')" aria-label="Close">
			<span aria-hidden="true">&times;</span>
		</button>
  </div>

  <div class="modal-body">
		<div class="text-primary fw-semibold ms-auto mb-25">
      Đang chọn: {{ tempSelectedOrganization?.name || 'Chưa chọn' }}
    </div>
    <div class="mb-1 position-relative">
      <div class="input-with-icon position-relative">
        <button type="button" class="btn-icon-inside left-icon">
          <i data-feather="search"></i>
        </button>
        <input
          type="text"
          class="form-control"
          placeholder="Tìm kiếm theo tên tổ chức"
          (input)="orgSearchText = $any($event.target).value || ''"
        />
      </div>
    </div>

    <div class="tree-wrap">
      <ng-container *ngFor="let node of listOrganization">
        <ng-container
          *ngTemplateOutlet="organizationTreeNodeTpl; context: { $implicit: node, level: 0, search: orgSearchText }"
        ></ng-container>
      </ng-container>
    </div>

    <ng-template #organizationTreeNodeTpl let-node let-level="level" let-search="search">
      <ng-container *ngIf="shouldShowNode(node, search)">
        <div
          class="tree-row cursor-pointer d-flex align-items-center"
          [ngStyle]="{'padding-left.px': 12 + level * 20}"
          [class.has-children]="node.children?.length"
          [class.active]="isTempSelected(node)"
          (click)="setTempOrganization(node)"
          (dblclick)="confirmOrganization(modal, node)"
        >
          <span class="tree-expand-holder d-flex align-items-center justify-content-center me-1 width-24px">
            <ng-container *ngIf="node.children?.length; else emptyHolder">
              <button
                type="button"
                class="toggle btn p-0 border-0 bg-transparent d-flex align-items-center width-24px"
                (click)="$event.stopPropagation(); node.showChildren = !node.showChildren"
                [attr.aria-label]="node.showChildren ? 'Collapse' : 'Expand'"
                tabindex="-1"
              >
                <i
                  class="feather"
                  [ngClass]="{
                    'icon-chevron-down': node.showChildren,
                    'icon-chevron-right': !node.showChildren
                  }"
                ></i>
              </button>
            </ng-container>
            <ng-template #emptyHolder>
              <span class="width-24px height-24px"></span>
            </ng-template>
          </span>
          <i class="feather node-icon icon-briefcase me-75"></i>
          <div class="node-text flex-grow-1">
            <span class="node-name fw-semibold">
              <strong>{{ node.name }}</strong>
            </span>
          </div>
          <span class="badge badge-light-primary ms-auto" *ngIf="isTempSelected(node)">Đang chọn</span>
        </div>
        <div *ngIf="node.children?.length && node.showChildren">
          <ng-container *ngFor="let child of node.children">
            <ng-container
              *ngTemplateOutlet="organizationTreeNodeTpl; context: { $implicit: child, level: level + 1, search: search }"
            ></ng-container>
          </ng-container>
        </div>
      </ng-container>
    </ng-template>
  </div>

  <div class="modal-footer border-0">
    <button type="button" class="btn btn-secondary" (click)="clearSelectedOrganization()">
      Bỏ chọn tổ chức
    </button>
    <button
      type="button"
      class="btn btn-primary-theme"
      [disabled]="!tempSelectedOrganization"
      (click)="confirmOrganization(modal)"
    >
      Chọn
    </button>
  </div>
</ng-template>