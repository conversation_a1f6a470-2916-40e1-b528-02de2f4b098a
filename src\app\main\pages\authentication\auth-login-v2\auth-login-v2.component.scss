@import "@core/scss/base/bootstrap-extended/include"; // Bootstrap includes
@import "@core/scss/base/pages/page-auth.scss";

.auth-help-icon {
  position: absolute;
  top: 10px;
  right: 10px;
}

.login-page {
  background-size: cover !important; /* Phủ toàn bộ vùng nền */
  background-repeat: no-repeat !important; /* Không lặp lại hình ảnh */
  background-position: center !important; /* Căn giữa hình ảnh */
  height: 100vh; /* Chiều cao toàn màn hình */
}
.gradient-text {
  background: linear-gradient(
    to left,
    rgba(0, 97, 255) 0%,
    rgba(0, 97, 255, 0.8) 45%,
    rgb(0, 97, 255, 0) 50%,
    rgba(0, 97, 255, 0.8) 55%,
    rgba(0, 97, 255) 100%
  );
  background-size: 200% auto;
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  -webkit-text-fill-color: transparent;
  animation: textShine 2.5s linear infinite;
}

@keyframes textShine {
  0% {
    background-position: 100% center;
  }
  100% {
    background-position: -100% center;
  }
}

.vpqh-footer-spacing {
  margin-bottom: 14px;
}
