{"name": "csign-sca-template", "version": "0.2.0", "scripts": {"ng": "ng", "start": "set NODE_OPTIONS=--max_old_space_size=8096 & ng serve", "start-hmr": "ng serve --configuration hmr --source-map=false --hmr-warning=false", "start-hmr-sourcemaps": "ng serve --configuration hmr --source-map=true --hmr-warning=false", "build": "node --max_old_space_size=8192 ./node_modules/@angular/cli/bin/ng build --configuration production", "build-stats": "node --max_old_space_size=8192 ./node_modules/@angular/cli/bin/ng build --stats-json", "build:prod-stats": "node --max_old_space_size=8192 ./node_modules/@angular/cli/bin/ng build --configuration production --stats-json", "bundle-analyzer": "webpack-bundle-analyzer dist/Csign/stats.json", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "format": "prettier --check \"**/*.{ts,js,json,html,css,scss,md}\""}, "private": true, "dependencies": {"@abacritt/angularx-social-login": "1.2.5", "@angular/animations": "14.3.0", "@angular/cdk": "^14.2.7", "@angular/common": "14.3.0", "@angular/compiler": "14.3.0", "@angular/core": "14.3.0", "@angular/flex-layout": "14.0.0-beta.41", "@angular/forms": "14.3.0", "@angular/google-maps": "14.2.7", "@angular/localize": "14.3.0", "@angular/material": "14.2.7", "@angular/platform-browser": "14.3.0", "@angular/platform-browser-dynamic": "14.3.0", "@angular/router": "14.3.0", "@circlon/angular-tree-component": "11.0.4", "@ctrl/ngx-csv": "4.0.0", "@ctrl/ngx-rightclick": "4.0.0", "@fullcalendar/angular": "5.7.1", "@fullcalendar/daygrid": "5.7.2", "@fullcalendar/interaction": "5.7.2", "@fullcalendar/list": "5.7.2", "@fullcalendar/timegrid": "5.7.2", "@mcarey1590/ngx-diff2html": "^1.0.0", "@ng-bootstrap/ng-bootstrap": "10.0.0", "@ng-select/ng-select": "9.0.2", "@ngx-translate/core": "14.0.0", "@sweetalert2/ngx-sweetalert2": "8.1.1", "@swimlane/ngx-datatable": "19.0.0", "angular-in-memory-web-api": "0.11.0", "angular-shepherd": "11.0.0", "animate.css": "4.1.1", "apexcharts": "3.23.0", "bootstrap": "4.6.0", "bs-stepper": "1.7.0", "chart.js": "2.9.4", "core-js": "3.14.0", "countup.js": "^2.9.0", "d3": "^7.9.0", "date-fns": "^2.30.0", "diff": "^5.2.0", "diff2html": "^3.4.48", "docx": "^9.5.1", "docx-preview": "^0.3.5", "feather-icons": "4.28.0", "file-saver": "^2.0.5", "flatpickr": "^4.6.9", "gitdiff-parser": "^0.2.2", "hammerjs": "2.0.8", "html2canvas": "^1.4.1", "katex": "0.13.11", "lodash": "4.17.21", "mammoth": "^1.9.1", "marked": "^4.3.0", "muuri": "^0.9.5", "ng-apexcharts": "1.5.8", "ng-block-ui": "3.0.2", "ng2-charts": "2.4.2", "ng2-dragula": "2.1.1", "ng2-file-upload": "1.4.0", "ng2-flatpickr": "^9.0.0", "ng2-nouislider": "1.8.3", "ngx-doc-viewer": "2.0.4", "ngx-extended-pdf-viewer": "^18", "ngx-highlightjs": "4.1.4", "ngx-markdown": "^14.0.1", "ngx-mask": "13.1.15", "ngx-monaco-editor": "^12.0.0", "ngx-perfect-scrollbar": "10.1.1", "ngx-plyr": "4.0.0", "ngx-quill": "^14.0.0", "ngx-swiper-wrapper": "10.0.0", "ngx-toastr": "14.0.0", "node-waves": "0.7.6", "nouislider": "15.1.1", "plyr": "3.6.8", "primeflex": "2.0.0", "primeicons": "^4.1.0", "primeng": "^13.4.1", "quill": "^1.3.7", "quill-better-table": "^1.2.10", "quill-table": "^1.0.0", "rxjs": "7.8.1", "sanitize-html": "^2.17.0", "screenfull": "^6.0.2", "sweetalert2": "^9.17.2", "swiper": "6.7.0", "tslib": "^2.3.0", "websocket": "^1.0.35", "xlsx": "^0.18.5", "zone.js": "0.11.8"}, "devDependencies": {"@angular-devkit/build-angular": "14.2.13", "@angular/cli": "14.2.13", "@angular/compiler-cli": "14.3.0", "@angular/language-service": "14.3.0", "@angularclass/hmr": "3.0.0", "@types/flatpickr": "^3.1.4", "@types/jasmine": "3.6.3", "@types/node": "12.11.1", "codelyzer": "6.0.1", "jasmine-core": "3.7.0", "karma": "6.3.0", "karma-chrome-launcher": "3.1.0", "karma-coverage": "2.0.3", "karma-jasmine": "4.0.0", "karma-jasmine-html-reporter": "1.5.0", "typescript": "4.7.4", "webpack-bundle-analyzer": "4.4.2"}}