<div class="container p-0">
  <app-breadcrumb [breadcrumb]="breadcrumbDefault"></app-breadcrumb>
  <div class="card">
    <div class="card-body">
      <ul 
        ngbNav #nav="ngbNav"
        [(activeId)]="activeTabId"
        (navChange)="onTabChange($event.nextId)" 
        class="nav-tabs"
      >
        <li ngbNavItem="all">
          <a ngbNavLink>Tất cả ({{ totalItem }})</a>
          <ng-template ngbNavContent>
            <form class="form form-vertical px-1" [formGroup]="formGetUsers">
              <div class="row">
                <div class="col-12 col-sm-4 col-xxl-4 ml-0 pl-0">
                  <div class="input-group input-group-merge mb-2">
                    <div class="input-group-prepend">
                      <span class="input-group-text" id="basic-addon-search2">
                        <i data-feather="search"></i>
                      </span>
                    </div>
                    <input
                      formControlName="search"
                      type="text"
                      class="form-control"
                      placeholder="Tìm kiếm theo tên hoặc email..."
                      aria-label="Search..."
                      aria-describedby="basic-addon-search2"
                    />
                  </div>
                </div>
                <div class="col-12 col-sm-2 ml-0 pl-0">
                  <fieldset class="form-group">
                    <div class="input-group custom-org-picker cursor-pointer" (click)="onSelectOrganizationClick($event)">
                      <input
                        class="form-control cursor-pointer bg-transparent"
                        [placeholder]="
                          role == 'SUPER_ADMIN' ? 'Tất cả tổ chức' : 'Tất cả phòng ban'
                        "
                        [value]="selectedOrganization?.name"
                        readonly
                      />
                    </div>
                  </fieldset>
                </div>
                <div class="col-12 col-sm-2 ml-0 pl-0">
                  <fieldset class="form-group">
                    <ng-select
                      formControlName="active"
                      [items]="listStatusUser"
                      bindLabel="label"
                      bindValue="value"
                      [clearable]="true"
                      placeholder="Tất cả trạng thái"
                    ></ng-select>
                  </fieldset>
                </div>
      
                <div class="p-0 ml-auto">
                  <div placement="top">
                    <button
                      type="button"
                      rippleEffect
                      class="btn btn-primary-theme"
                      (click)="addUser()"
                    >
                      Thêm người dùng
                    </button>
                  </div>
                </div>
              </div>
            </form>
            <ngx-datatable
              #tableRowDetails
              [rows]="listUser"
              [rowHeight]="58"
              class="bootstrap core-bootstrap cursor"
              [columnMode]="ColumnMode.force"
              [headerHeight]="40"
              [footerHeight]="50"
              [scrollbarH]="true"
              [limit]="12"
              (activate)="onActivate($event)"
              [count]="totalItem"
              [offset]="page - 1"
              [externalPaging]="true"
              (page)="setPage($event)"
            >
              <ngx-datatable-column name="Họ tên" prop="fullname" [width]="150" [sortable]="false">
              </ngx-datatable-column>
              <ngx-datatable-column name="Email" prop="email" [width]="150" [sortable]="false">
              </ngx-datatable-column>
      
              <!-- <ngx-datatable-column name="Số điện thoại" prop="phone" [width]="150" [sortable]="false">
              </ngx-datatable-column> -->
              <ngx-datatable-column
                [name]="role == 'SUPER_ADMIN' ? 'Tổ chức' : 'Phòng ban'"
                prop="organization_memberships"
                [width]="150"
                [sortable]="false"
              >
                <ng-template
                  let-status="value"
                  let-row="row"
                  ngx-datatable-cell-template
                >
                  {{ row.organization_memberships[0]?.organization_name }}
                </ng-template>
              </ngx-datatable-column>
              <ngx-datatable-column
                name="Chức vụ"
                prop="position.title"
                [width]="150"
                [sortable]="false"
              >
              </ngx-datatable-column>
      
              <ngx-datatable-column name="Vai trò" prop="role" [width]="150" [sortable]="false">
                <ng-template
                  let-status="value"
                  let-row="row"
                  ngx-datatable-cell-template
                >
                  <div
                    class="badge badge-pill"
                    [ngClass]="{
                      'badge-light-dark': row.system_role == 'USER',
                      'badge-light-primary': row.system_role == 'ADMIN',
                      'badge-light-success': row.system_role == 'SUPER_ADMIN'
                    }"
                  >
                    {{
                      row.system_role == "USER"
                        ? "Người dùng"
                        : row.system_role == "ADMIN"
                        ? "Quản trị viên"
                        : "Quản trị cấp cao"
                    }}
                  </div>
                </ng-template>
              </ngx-datatable-column>
              <ngx-datatable-column name="Trạng thái" [sortable]="false" prop="active" [width]="200">
                <ng-template ngx-datatable-cell-template let-row="row">
                  <div
                    class="badge badge-pill"
                    [ngClass]="{
                      'badge-light-success': row.active === true,
                      'badge-light-danger': row.active === false
                    }"
                  >
                    {{ row.active ? "Hoạt động" : "Đã khoá" }}
                  </div>
                </ng-template>
              </ngx-datatable-column>
              <ngx-datatable-column name="Hành động" [width]="120" [sortable]="false">
                <ng-template ngx-datatable-cell-template let-row="row">
                  <span ngbDropdown container="body">
                    <a
                      ngbDropdownToggle
                      href="javascript:void(0);"
                      class="hide-arrow"
                      id="dropdownBrowserState"
                      data-toggle="dropdown"
                      aria-haspopup="true"
                      aria-expanded="false"
                      placement="top"
                      ngbTooltip="Sửa"
                      container="body"
                      (click)="editUser(row)"
                    >
                      <i data-feather="edit-2" class="mx-50"></i>
                    </a>
                    <a
                      *ngIf="row.active"
                      ngbDropdownToggle
                      href="javascript:void(0);"
                      class="hide-arrow color-333"
                      id="dropdownBrowserState"
                      data-toggle="dropdown"
                      aria-haspopup="true"
                      aria-expanded="false"
                      placement="top"
                      container="body"
                      ngbTooltip="Vô hiệu hoá"
                      (click)="updateStatus(row, 'False')"
                    >
                      <i data-feather="slash" class="mx-50"></i>
                    </a>
                    <a
                      *ngIf="!row.active"
                      ngbDropdownToggle
                      href="javascript:void(0);"
                      class="hide-arrow"
                      id="dropdownBrowserState"
                      data-toggle="dropdown"
                      aria-haspopup="true"
                      aria-expanded="false"
                      placement="top"
                      container="body"
                      ngbTooltip="Kích hoạt lại"
                      (click)="updateStatus(row, 'True')"
                    >
                      <i data-feather="check-circle" class="mx-50"></i>
                    </a>
                    <a
                      *ngIf="role == 'SUPER_ADMIN'"
                      ngbDropdownToggle
                      href="javascript:void(0);"
                      class="hide-arrow color-333"
                      id="dropdownBrowserState"
                      data-toggle="dropdown"
                      aria-haspopup="true"
                      aria-expanded="false"
                      placement="top"
                      container="body"
                      ngbTooltip="Cấp lại mật khẩu"
                      (click)="isPopoverEnabled = true"
                      [ngbPopover]="isPopoverEnabled ? resetPassword : null"
                      #popover="ngbPopover"
                      [autoClose]="'outside'"
                    >
                      <i data-feather="settings" class="mx-50"></i>
                    </a>
                    <a
                      ngbDropdownToggle
                      href="javascript:void(0);"
                      class="hide-arrow color-333"
                      id="dropdownBrowserState"
                      data-toggle="dropdown"
                      aria-haspopup="true"
                      aria-expanded="false"
                      placement="top"
                      container="body"
                      ngbTooltip="Xóa"
                      (click)="deleteUser(row)"
                    >
                      <i data-feather="trash-2" class="mx-50"></i>
                    </a>
                  </span>
                </ng-template>
              </ngx-datatable-column>
            </ngx-datatable>
          </ng-template>
        </li>
        <li [ngbNavItem]="RegistrationQueueStatus.TO_REVIEW" *ngIf="role == 'SUPER_ADMIN'">
          <a ngbNavLink>Chờ phê duyệt ({{ registrationCount.toReview }})</a>
          <ng-template ngbNavContent>
            <div class="d-flex">
              <div class="col-12 col-sm-4 col-xxl-4 ml-0 pl-0">
                <div class="input-group input-group-merge mb-2">
                  <div class="input-group-prepend">
                    <span class="input-group-text" id="basic-addon-search2">
                      <i data-feather="search"></i>
                    </span>
                  </div>
                  <input
                    type="text"
                    class="form-control"
                    [(ngModel)]="registrationSearch.TO_REVIEW"
                    (ngModelChange)="onSearchChange(RegistrationQueueStatus.TO_REVIEW, $event)"
                    placeholder="Tìm kiếm theo tên hoặc email..."
                    aria-label="Search..."
                    aria-describedby="basic-addon-search2"
                  />
                </div>
              </div>
                    
              <div class="p-0 ml-auto">
                <div placement="top">
                  <button
                    type="button"
                    rippleEffect
                    class="btn btn-primary-theme"
                    (click)="addUser()"
                  >
                    Thêm người dùng
                  </button>
                </div>
              </div>
            </div>
              
            <app-registration-queue
              [status]="activeTabId"
              [registrationQueue]="registrationQueue"
              [registrationQueueTotal]="registrationQueueTotal"
              [registrationQueuePage]="registrationQueuePage.TO_REVIEW"
              (registrationQueuePageChange)="registrationQueuePageChange($event)"
              (update)="updateRegistration($event)"
            ></app-registration-queue>
          </ng-template>
        </li>
        <li [ngbNavItem]="RegistrationQueueStatus.APPROVED" *ngIf="role == 'SUPER_ADMIN'">
          <a ngbNavLink>Đã phê duyệt ({{ registrationCount.approved }})</a>
          <ng-template ngbNavContent>
            <div class="d-flex">
              <div class="col-12 col-sm-4 col-xxl-4 ml-0 pl-0">
                <div class="input-group input-group-merge mb-2">
                  <div class="input-group-prepend">
                    <span class="input-group-text" id="basic-addon-search2">
                      <i data-feather="search"></i>
                    </span>
                  </div>
                  <input
                    type="text"
                    class="form-control"
                    [(ngModel)]="registrationSearch.APPROVED"
                    (ngModelChange)="onSearchChange(RegistrationQueueStatus.APPROVED, $event)"
                    placeholder="Tìm kiếm theo tên hoặc email..."
                    aria-label="Search..."
                    aria-describedby="basic-addon-search2"
                  />
                </div>
              </div>
                    
              <div class="p-0 ml-auto">
                <div placement="top">
                  <button
                    type="button"
                    rippleEffect
                    class="btn btn-primary-theme"
                    (click)="addUser()"
                  >
                    Thêm người dùng
                  </button>
                </div>
              </div>
            </div>

            <app-registration-queue
              [status]="activeTabId"
              [registrationQueue]="registrationQueue"
              [registrationQueueTotal]="registrationQueueTotal"
              [registrationQueuePage]="registrationQueuePage.APPROVED"
              (registrationQueuePageChange)="registrationQueuePageChange($event)"
              (update)="updateRegistration($event)"
            ></app-registration-queue>
          </ng-template>
        </li>
        <li [ngbNavItem]="RegistrationQueueStatus.REJECTED" *ngIf="role == 'SUPER_ADMIN'">
          <a ngbNavLink>Đã từ chối ({{ registrationCount.rejected }})</a>
          <ng-template ngbNavContent>
            <div class="d-flex">
              <div class="col-12 col-sm-4 col-xxl-4 ml-0 pl-0">
                <div class="input-group input-group-merge mb-2">
                  <div class="input-group-prepend">
                    <span class="input-group-text" id="basic-addon-search2">
                      <i data-feather="search"></i>
                    </span>
                  </div>
                  <input
                    type="text"
                    class="form-control"
                    [(ngModel)]="registrationSearch.REJECTED"
                    (ngModelChange)="onSearchChange(RegistrationQueueStatus.REJECTED, $event)"
                    placeholder="Tìm kiếm theo email..."
                    aria-label="Search..."
                    aria-describedby="basic-addon-search2"
                  />
                </div>
              </div>
                    
              <div class="p-0 ml-auto">
                <div placement="top">
                  <button
                    type="button"
                    rippleEffect
                    class="btn btn-primary-theme"
                    (click)="addUser()"
                  >
                    Thêm người dùng
                  </button>
                </div>
              </div>
            </div>

            <app-registration-queue
              [status]="activeTabId"
              [registrationQueue]="registrationQueue"
              [registrationQueueTotal]="registrationQueueTotal"
              [registrationQueuePage]="registrationQueuePage.REJECTED"
              (registrationQueuePageChange)="registrationQueuePageChange($event)"
              (update)="updateRegistration($event)"
            ></app-registration-queue>
          </ng-template>
        </li>
      </ul>
      <div [ngbNavOutlet]="nav" class="mt-2"></div>
    </div>
  </div>
</div>

<ng-template #resetPassword>
  <form
    class="rp-card mx-auto"
    [formGroup]="formResetPassword"
    (ngSubmit)="onSubmit()"
  >
    <label for="basic-default-password1" class="form-label fw-semibold">Mật khẩu mới
      <span class="text-danger"> *</span>
    </label>
    <div class="input-group input-group-merge form-password-toggle">
      <input
        [type]="mergedPwdShow ? 'text' : 'password'"
        class="form-control"
        name="password"
        autocomplete="off"
        formControlName="password"
        id="basic-default-password1"
        placeholder="Nhập"
        aria-describedby="basic-default-password1"
        required
      />
      <div class="input-group-append" (click)="mergedPwdShow = !mergedPwdShow">
        <span class="input-group-text cursor-pointer"
          ><i
            class="feather"
            [ngClass]="{
              'icon-eye-off': mergedPwdShow,
              'icon-eye': !mergedPwdShow
            }"
          ></i
        ></span>
      </div>
    </div>
    <div class="col-sm-12 p-0">
      <div
        *ngIf="(submitted || f.password?.touched) && f.password?.errors"
        class="invalid-form"
      >
        <small
          *ngIf="f.password?.errors['required']"
          class="form-text text-danger"
        >
          Vui lòng nhập mật khẩu mới
        </small>
        <small
          *ngIf="f.password?.errors['minlength']"
          class="form-text text-danger"
        >
          Mật khẩu phải có ít nhất 6 ký tự
        </small>
        <small
          *ngIf="f.password?.errors['pattern'] && !f.password?.errors['minlength'] && !f.password?.errors['required']"
          class="form-text text-danger"
        >
          Mật khẩu phải có ít nhất 1 chữ in hoa và 1 ký tự đặc biệt
        </small>
      </div>
    </div>

    <div class="mt-1 d-flex justify-content-end">
      <button type="button" class="btn btn-secondary mr-50" (click)="cancel()">
        Hủy
      </button>
      <button type="submit" class="btn btn-primary" [disabled]="formResetPassword.invalid">Xác nhận</button>
    </div>
  </form>
</ng-template>


<!-- Modal chọn tổ chức -->
<ng-template #modalSelectOrg let-modal>
  <div class="modal-header">
    <h4 class="modal-title">Chọn tổ chức</h4>
		<button type="button" class="close" (click)="modal.dismiss('Cross click')" aria-label="Close">
			<span aria-hidden="true">&times;</span>
		</button>
  </div>

  <div class="modal-body">
		<div class="text-primary fw-semibold ms-auto mb-25">
      Đang chọn: {{ tempSelectedOrganization?.name || 'Chưa chọn' }}
    </div>
    <div class="mb-1 position-relative">
      <div class="input-with-icon position-relative">
        <button type="button" class="btn-icon-inside left-icon">
          <i data-feather="search"></i>
        </button>
        <input
          type="text"
          class="form-control"
          placeholder="Tìm kiếm theo tên tổ chức"
          (input)="orgSearchText = $any($event.target).value || ''"
        />
      </div>
    </div>

    <div class="tree-wrap">
      <ng-container *ngFor="let node of listOrgan">
        <ng-container
          *ngTemplateOutlet="organizationTreeNodeTpl; context: { $implicit: node, level: 0, search: orgSearchText }"
        ></ng-container>
      </ng-container>
    </div>

    <ng-template #organizationTreeNodeTpl let-node let-level="level" let-search="search">
      <ng-container *ngIf="shouldShowNode(node, search)">
        <div
          class="tree-row cursor-pointer d-flex align-items-center"
          [ngStyle]="{'padding-left.px': 12 + level * 20}"
          [class.has-children]="node.children?.length"
          [class.active]="isTempSelected(node)"
          (click)="setTempOrganization(node)"
          (dblclick)="confirmOrganization(modal, node)"
        >
          <span class="tree-expand-holder d-flex align-items-center justify-content-center me-1 width-24px">
            <ng-container *ngIf="node.children?.length; else emptyHolder">
              <button
                type="button"
                class="toggle btn p-0 border-0 bg-transparent d-flex align-items-center width-24px"
                (click)="$event.stopPropagation(); node.showChildren = !node.showChildren"
                [attr.aria-label]="node.showChildren ? 'Collapse' : 'Expand'"
                tabindex="-1"
              >
                <i
                  class="feather"
                  [ngClass]="{
                    'icon-chevron-down': node.showChildren,
                    'icon-chevron-right': !node.showChildren
                  }"
                ></i>
              </button>
            </ng-container>
            <ng-template #emptyHolder>
              <span class="width-24px height-24px"></span>
            </ng-template>
          </span>
          <i class="feather node-icon icon-briefcase me-75"></i>
          <div class="node-text flex-grow-1">
            <span class="node-name fw-semibold">
              <strong>{{ node.name }}</strong>
            </span>
          </div>
          <span class="badge badge-light-primary ms-auto" *ngIf="isTempSelected(node)">Đang chọn</span>
        </div>
        <div *ngIf="node.children?.length && node.showChildren">
          <ng-container *ngFor="let child of node.children">
            <ng-container
              *ngTemplateOutlet="organizationTreeNodeTpl; context: { $implicit: child, level: level + 1, search: search }"
            ></ng-container>
          </ng-container>
        </div>
      </ng-container>
    </ng-template>
  </div>

  <div class="modal-footer border-0">
    <button type="button" class="btn btn-secondary" (click)="clearSelectedOrganization()">
      Bỏ chọn tổ chức
    </button>
    <button
      type="button"
      class="btn btn-primary-theme"
      [disabled]="!tempSelectedOrganization"
      (click)="confirmOrganization(modal)"
    >
      Chọn
    </button>
  </div>
</ng-template>
