import {
  <PERSON>mpo<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  HostListener,
  ViewEncapsulation,
} from "@angular/core";
import { Router, NavigationEnd } from "@angular/router";

import { Subject } from "rxjs";
import { take, takeUntil, filter } from "rxjs/operators";
import { PerfectScrollbarDirective } from "ngx-perfect-scrollbar";

import { CoreConfigService } from "@core/services/config.service";
import { CoreMenuService } from "@core/components/core-menu/core-menu.service";
import { CoreSidebarService } from "@core/components/core-sidebar/core-sidebar.service";
import { getAppName as getAppNameHelper, DEFAULT_LOGO_IMAGE } from "app/shared/image.helper";
import { ChangeDetectorRef } from "@angular/core";
import { CmsService } from "app/main/apps/cms/cms.service";
import { AuthenticationService } from "app/auth/service";

@Component({
  selector: "vertical-menu",
  templateUrl: "./vertical-menu.component.html",
  styleUrls: ["./vertical-menu.component.scss"],
  encapsulation: ViewEncapsulation.None,
})
export class VerticalMenuComponent implements OnInit, OnDestroy {
  coreConfig: any;
  menu: any;
  isCollapsed: boolean;
  isScrolled: boolean = false;
  getAppName = getAppNameHelper;
  defaultLogo = DEFAULT_LOGO_IMAGE;

  // Private
  private _unsubscribeAll: Subject<any>;
  private _hasCmsRoles = false;
  private _savedHiddenBeforeCms: boolean | null = null;
  private _baseMenu: any[] = [];
  private _cmsMenu: any[] = [];
  public userRole: string;

  /**
   * Constructor
   *
   * @param {CoreConfigService} _coreConfigService
   * @param {CoreMenuService} _coreMenuService
   * @param {CoreSidebarService} _coreSidebarService
   * @param {Router} _router
   */
  constructor(
    private _coreConfigService: CoreConfigService,
    private _coreMenuService: CoreMenuService,
    private _coreSidebarService: CoreSidebarService,
    private _router: Router,
    private _cms: CmsService,
    private cdr: ChangeDetectorRef,
    private authService: AuthenticationService
  ) {
    // Set the private defaults
    this._unsubscribeAll = new Subject();

    this.userRole = this.authService.currentUserValue?.role;
  }

  @ViewChild(PerfectScrollbarDirective, { static: false })
  directiveRef?: PerfectScrollbarDirective;

  // Lifecycle Hooks
  // -----------------------------------------------------------------------------------------------------

  /**
   * On Init
   */
  ngOnInit(): void {
    // Subscribe config change
    this._coreConfigService.config
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe((config) => {
        this.coreConfig = config;

        // ✅ Cập nhật isCollapsed khi config thay đổi
        if (config.layout && config.layout.menu) {
          this.isCollapsed = config.layout.menu.collapsed;
        }
      });

    if (this._isQlVbRoot(this._router.url)) {
      this._deferSetConfig({
        layout: { menu: { hidden: false, collapsed: false } },
      });
    }

    // ✅ Lấy trạng thái collapsed ban đầu từ sidebar
    const sidebar = this._coreSidebarService.getSidebarRegistry("menu");
    if (sidebar) {
      this.isCollapsed = sidebar.collapsed;
    }

    // Close the menu on router NavigationEnd (Required for small screen to close the menu on select)
    this._router.events
      .pipe(
        filter((event) => event instanceof NavigationEnd),
        takeUntil(this._unsubscribeAll)
      )
      .subscribe(() => {
        const menuSidebar =
          this._coreSidebarService.getSidebarRegistry("menu");
        if (menuSidebar) {
          menuSidebar.close();
        }
      });

    // Scroll to active on navigation end
    this._router.events
      .pipe(
        filter((event) => event instanceof NavigationEnd),
        take(1)
      )
      .subscribe(() => {
        setTimeout(() => {
          this.directiveRef?.scrollToElement(
            ".navigation .active",
            -180,
            500
          );
        });
      });

    // Get current menu
    this._coreMenuService.onMenuChanged
      .pipe(filter((v) => v !== null), takeUntil(this._unsubscribeAll))
      .subscribe(() => {
        const current = this._coreMenuService.getCurrentMenu() || [];
        this.menu = current;

        // ⭐ MỚI: luôn split lại base/cms mỗi lần menu thay đổi
        const { base, cms } = this._splitMenuByCms(current);
        this._baseMenu = base;
        this._cmsMenu = cms;

        // áp menu phù hợp route hiện tại
        this._applyCmsSidebarRule(this._router.url);
      });

    this._applyCmsSidebarRule(this._router.url);
    this._router.events
      .pipe(
        filter((e) => e instanceof NavigationEnd),
        takeUntil(this._unsubscribeAll)
      )
      .subscribe((e: NavigationEnd) =>
        this._applyCmsSidebarRule(e.urlAfterRedirects || e.url)
      );

    this._cms.ensureCmsRolesLoaded().subscribe(); // fire & forget

    this._cms.cmsRoles$
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe((roles) => {
        this._hasCmsRoles = Array.isArray(roles) && roles.length > 0;

        // ⭐ MỚI: luôn đọc lại user hiện tại (sau logout sẽ là null)
        const currentUser = this.authService.currentUserValue;
        this.userRole = currentUser?.role;
        const isPrivileged =
          this.userRole === "ADMIN" || this.userRole === "SUPER_ADMIN";

        // Với user thường có CMS role ở /quan-ly-van-ban
        if (!isPrivileged && this._isQlVbRoot(this._router.url)) {
          if (this._hasCmsRoles) {
            this._deferSetConfig({
              layout: { menu: { hidden: false, collapsed: false } },
            });
          } else {
            this._deferSetConfig({ layout: { menu: { hidden: true } } });
          }
        }

        this._applyCmsSidebarRule(this._router.url);
        this.cdr.markForCheck();
      });
  }

  /**
   * On Destroy
   */
  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions
    this._unsubscribeAll.next(null);
    this._unsubscribeAll.complete();
  }

  // Public / Helper Methods
  // -----------------------------------------------------------------------------------------------------

  // ⭐ MỚI: chuẩn hoá URL (bỏ domain, query, hash, prefix /cls)
  private _normalize(url: string): string {
    try {
      const raw = (url || this._router.url || "").toString();
      const noHash = raw.split("#")[0];
      const noQuery = noHash.split("?")[0];
      return noQuery
        .replace(/^https?:\/\/[^/]+/i, "")
        .replace(/^\/cls(?=\/)/i, "");
    } catch {
      return url || "";
    }
  }

  // ⭐ MỚI: check đúng root /quan-ly-van-ban
  private _isQlVbRoot(url: string): boolean {
    const u = this._normalize(url);
    return /^\/?quan-ly-van-ban\/?$/i.test(u);
  }

  private _isCms(url: string): boolean {
    const u = this._normalize(url);
    return /(^|\/)cms(\/|$)/i.test(u);
  }

  private _isPublic(url: string): boolean {
    const u = url.replace(/^\/cls(?=\/)/i, "");
    const isLanding = /(^|\/)(pages\/)?landing(\/|$|\?|#)/i.test(u);
    const isHome = /(^|\/)home(\/|$|\?|#)/i.test(u);
    const isAuth =
      /authentication|auth\-login|auth\-register|login|register/i.test(u);
    const isMiscErr = /(^|\/)(pages\/)?miscellaneous\/error(\/|$|\?|#)/i.test(
      u
    );
    const isArticles = /(^|\/)(pages\/)?articles(\/|$|\?|#)/i.test(u);
    const isArticle = /(^|\/)(pages\/)?article(\/|$|\?|#)/i.test(u);
    return isLanding || isHome || isAuth || isMiscErr || isArticles || isArticle;
  }

  private _lastMenuSig = "";
  private _deferSetConfig(next: any) {
    Promise.resolve().then(() => {
      this._coreConfigService.setConfig(next, { emitEvent: true });
    });
  }

  private _splitMenuByCms(items: any[]): { base: any[]; cms: any[] } {
    const isCmsItem = (it: any): boolean => {
      if (Array.isArray(it?.children) && it.children.length) {
        return it.children.every((c: any) => isCmsItem(c));
      }
      const url = (it?.url || it?.path || "").toString();
      return url.startsWith("/cms/");
    };
    const clone = (x: any) => JSON.parse(JSON.stringify(x ?? []));
    const base: any[] = [],
      cms: any[] = [];
    (items || []).forEach((it) => {
      if (Array.isArray(it?.children) && it.children.length) {
        const kidsBase: any[] = [],
          kidsCms: any[] = [];
        it.children.forEach((ch: any) =>
          isCmsItem(ch) ? kidsCms.push(ch) : kidsBase.push(ch)
        );
        if (kidsBase.length) base.push({ ...it, children: clone(kidsBase) });
        if (kidsCms.length) cms.push({ ...it, children: clone(kidsCms) });
      } else {
        (isCmsItem(it) ? cms : base).push(clone(it));
      }
    });
    return { base, cms };
  }

  private _setMenu(items: any[]) {
    const sig = JSON.stringify(items ?? []);
    if (sig === this._lastMenuSig) return; // menu không đổi -> bỏ
    this._lastMenuSig = sig;

    const svc: any = this._coreMenuService as any;

    if (typeof svc.setMenu === "function") {
      svc.setMenu("main", items);
      return;
    }
    // Fallback SDK cũ
    try {
      if (
        typeof svc.isRegistered === "function"
          ? svc.isRegistered("main")
          : true
      ) {
        svc.unregister?.("main");
      }
    } catch {}
    svc.register?.("main", items);
    svc.setCurrentMenu?.("main");
  }


  private _isWorkspace(url: string): boolean {
    const u = this._normalize(url);
    return /^\/?quan-ly-van-ban\/workspace(\/|$)/i.test(u);
  }
  
  private _applyCmsSidebarRule(url: string) {
    const isCmsUrl = this._isCms(url);
    const isWorkspace = this._isWorkspace(url);
    const isQlVbRoot = this._isQlVbRoot(url);
    const isPublicUrl = this._isPublic(url);
    const currentHidden = !!this.coreConfig?.layout?.menu?.hidden;

    // luôn refresh role mỗi lần áp dụng rule
    const currentUser = this.authService.currentUserValue;
    this.userRole = currentUser?.role;
    const isPrivileged =
      this.userRole === "ADMIN" || this.userRole === "SUPER_ADMIN";

    // ===== 0. MỌI TRANG PUBLIC (login, landing, articles...) → CHỈ ẨN SIDEBAR, KHÔNG ĐỤNG TỚI MENU =====
    if (isPublicUrl) {
      this._deferSetConfig({
        layout: { menu: { hidden: true, collapsed: true } },
      });

      const sb = this._coreSidebarService.getSidebarRegistry("menu");
      if (sb?.forceClose) sb.forceClose();
      else sb?.close?.();
      return;
    }
    if (isWorkspace) {
      if (!currentHidden) {
        this._deferSetConfig({ layout: { menu: { hidden: true, collapsed: true } } });
      }
      const sb = this._coreSidebarService.getSidebarRegistry('menu');
      if (sb?.forceClose) sb.forceClose(); else sb?.close?.();
      this._setMenu([...this._baseMenu]); // giữ base nếu cần
      return;
    }

    // ===== 1. ADMIN / SUPER_ADMIN → luôn có sidebar (ở tất cả route nội bộ) =====
    if (isPrivileged) {
      if (currentHidden) {
        this._deferSetConfig({
          layout: { menu: { hidden: false, collapsed: false } },
        });
      }
      this._setMenu([...this._baseMenu, ...this._cmsMenu]);
      return;
    }

    // ===== 2. USER có CMS role + /quan-ly-van-ban → hiện sidebar, chỉ base menu =====
    if (this._hasCmsRoles && isQlVbRoot) {
      if (currentHidden) {
        this._deferSetConfig({
          layout: { menu: { hidden: false, collapsed: false } },
        });
      }
      this._setMenu([...this._baseMenu]);
      return;
    }

    // ===== 3. USER có CMS role nhưng KHÔNG ở /cms/**=====
    if (this._hasCmsRoles && !isCmsUrl) {
      if (!currentHidden) {
        this._deferSetConfig({
          layout: { menu: { hidden: true, collapsed: true } },
        });
      }
      const sb = this._coreSidebarService.getSidebarRegistry("menu");
      if (sb?.forceClose) sb.forceClose();
      else sb?.close?.();
      this._setMenu([...this._baseMenu]);
      return;
    }

    // ===== 4. Đang ở /cms/** → hiện sidebar + menu cms =====
    if (isCmsUrl) {
      if (currentHidden) {
        this._deferSetConfig({
          layout: { menu: { hidden: false, collapsed: false } },
        });
      }
      this._setMenu([...this._baseMenu, ...this._cmsMenu]);
      return;
    }

    // ===== 5. Các route khác (user thường) → ẩn sidebar (giữ logic cũ) =====
    if (!currentHidden) {
      this._deferSetConfig({ layout: { menu: { hidden: true } } });
    }
    this._setMenu([...this._baseMenu]);
  }

  /**
   * On Sidebar scroll set isScrolled as true
   */
  onSidebarScroll(): void {
    if (this.directiveRef?.position(true).y > 3) {
      this.isScrolled = true;
    } else {
      this.isScrolled = false;
    }
  }

  /**
   * Toggle sidebar (màn hình nhỏ - nút X)
   */
  toggleSidebar(): void {
    const sidebar = this._coreSidebarService.getSidebarRegistry("menu");
    if (!sidebar) {
      console.warn("Menu sidebar not found");
      return;
    }

    // ✅ Sử dụng forceClose thay vì close
    if (sidebar["forceClose"]) {
      sidebar["forceClose"]();
    } else {
      sidebar.close(); // Fallback
    }
  }

  /**
   * Toggle sidebar collapsed status (cho màn hình lớn)
   * Collapse/Expand menu chính
   */
  toggleSidebarCollapsible(): void {
    const sidebar = this._coreSidebarService.getSidebarRegistry("menu");

    if (!sidebar) {
      console.warn("Menu sidebar not found in registry");
      return;
    }

    // Toggle trạng thái collapsed
    const newCollapsedState = !this.isCollapsed;

    // Cập nhật config
    this._coreConfigService.setConfig(
      { layout: { menu: { collapsed: newCollapsedState } } },
      { emitEvent: true }
    );

    // Cập nhật local state
    this.isCollapsed = newCollapsedState;

    // Toggle sidebar
    sidebar.toggleCollapsible();
  }
}
