<div
  [ngClass]="{ 'custom-dialog': true, 'maximized custom-maximize': isMaximized }"
  class="h-100 container-chatbot"
>
  <!-- minimize button -->
  <div class="w-100 d-flex justify-content-end">
    <!-- <img
      class="cursor-pointer icon-button"
      (click)="closeCompareChatbot()"
      src="assets/images/icons/x.svg"
      alt="x"
    /> -->
    <img
      class="cursor-pointer icon-button"
      (click)="closeCompareChatbot()"
      src="assets/images/icons/minimum.svg"
      alt="minimize"
      *ngIf="isMaximized"
      ngbTooltip="Thu nhỏ"
      container="body"
    />
  </div>
  <!-- minimize button-->
  <div class="chat-content mt-1 p-0" #chatContainer>
    <div
      *ngFor="let message of messages; let i = index; let last = last"
      class="message-bubble p-0"
    >
      <p class="gradient-text" *ngIf="last && !message.reason && !doneChatBot">
        Đang so sánh ...
      </p>
      <!-- thinking -->
      <span
        class="collapse-icon"
        *ngIf="message.thinking"
        id="chatbot"
        [ngClass]="{
          user: message.role == 'user',
          bot: message.role == 'assistant'
        }"
      >
        <ngb-accordion #acc="ngbAccordion" activeIds="ngb-panel-1">
          <ngb-panel id="ngb-panel-1" [open]="true">
            <ng-template ngbPanelTitle>
              <span class="lead collapse-title card-title"
                ><strong class="font-sm"> Tiến trình tư duy </strong>
              </span>
            </ng-template>
            <ng-template ngbPanelContent>
              <p
                #editable
                [attr.data-index]="i"
                [attr.data-type]="'thinking'"
                class="thinking"
                [innerHTML]="message.thinking | markdown"
              ></p>
            </ng-template>
          </ngb-panel>
        </ngb-accordion>
      </span>
      <!-- thinking -->

      <p
        *ngIf="message.reason"
        class="answer"
        [ngClass]="{
          user: message.role == 'user',
          bot: message.role == 'assistant'
        }"
      >
        <span>
          <b>Diễn giải:</b>
          <span
            [attr.data-index]="i"
            [attr.data-type]="'reason'"
            [innerHTML]="message.reason | markdown"
            [contentEditable]="contentEditable"
            #editable
          ></span>
        </span>
      </p>
      <p *ngIf="message.conclusion" class="answer">
        <span>
          <b style="color: #000 !important">Kết luận:</b>
          <span
            class="{{ getConclusionClass(message.conclusion) }}"
            [attr.data-index]="i"
            [attr.data-type]="'conclusion'"
            [innerHTML]="message.conclusion"
            [contentEditable]="contentEditable"
            #editable
          ></span>
        </span>
      </p>
      <p *ngIf="message.solution" class="answer">
        <span>
          <b style="color: #000 !important">Giải pháp đề xuất:</b>
          <span
            [attr.data-type]="'solution'"
            [attr.data-index]="i"
            [innerHTML]="message.solution | markdown"
            [contentEditable]="contentEditable"
            #editable
          ></span>
        </span>
      </p>
      <p class="answer user wrap-text" *ngIf="message.question">
        <span *ngIf="message.question">
          <span [innerHTML]="message.question"></span>
        </span>
      </p>
      <!-- <span
        (click)="contentEditable = true; editCompareChatbot(i)"
        *ngIf="doneChatBot && message.solution"
        ngbTooltip="Chỉnh sửa"
        container="body"
        class="cursor-pointer"
      >
        <img
          src="assets/images/icons/pencil.svg"
          alt="pencil"
          class="icon-button"
        />
      </span>
      <span
        class="text-primary cursor-pointer"
        (click)="saveResultCompare()"
        *ngIf="doneChatBot && message.solution"
        ngbTooltip="Lưu kết quả"
        container="body"
      >
        <img
          src="assets/images/icons/tick.svg"
          class="icon-button"
          alt="tick"
        />
      </span> -->
      <span
        class="scroll-to-bottom"
        *ngIf="showScrollButton"
        (click)="scrollToBottom()"
        ><i data-feather="arrow-down" size="24"></i
      ></span>
    </div>
  </div>

  <div class="contain-input-chatbot">
    <!-- Ô NHẬP TIN NHẮN -->
    <!-- 
    <div class="chat-input-wrapper">
      <textarea
        spellcheck="false"
        id="queryChatbot"
        [(ngModel)]="userInput"
        (keydown)="sendMessage($event)"
        (input)="autoResize($event)"
        class="chat-input"
        placeholder="Nhập truy vấn của bạn vào đây"
        rows="1"
        [disabled]="!bodyChat.item2"
      ></textarea>

      <div class="chat-button-wrapper">
        <button
          class="chat-send-button"
          (click)="clickSendMessage()"
          *ngIf="doneChatBot"
          [ngClass]="{ 'disabled-button': userInput == '' }"
        >
          <img src="assets/images/icons/send.svg" alt="send " />
        </button>
        <button
          class="chat-cancel-button"
          (click)="cancelRequest()"
          *ngIf="!doneChatBot"
        >
          <img src="assets/images/icons/stop.svg" alt="send " />
        </button>
      </div>
    </div> -->
  </div>
</div>
