import { Component, OnInit } from "@angular/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { HttpClient } from "@angular/common/http";
import { CoreConfigService } from "@core/services/config.service";
import { takeUntil } from "rxjs/operators";
import { Subject } from "rxjs";
import { environment } from "environments/environment";
import { ToastrService } from "ngx-toastr";

@Component({
  selector: "app-auth-reset-password",
  templateUrl: "./auth-reset-password.component.html",
  styleUrls: ["./auth-reset-password.component.scss"],
})
export class AuthResetPasswordComponent implements OnInit {
  resetPasswordForm: FormGroup;
  uid: string;
  token: string;
  public coreConfig: any;
  private _unsubscribeAll: Subject<any>;
  public isExpired: boolean = false;
  /**
   * Constructor
   *
   * @param {CoreConfigService} _coreConfigService
   * @param {FormBuilder} _formBuilder
   *
   */
  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private http: HttpClient,
    private router: Router,
    private _coreConfigService: CoreConfigService,
    private toastr: ToastrService
  ) {
    this._unsubscribeAll = new Subject();
    this.resetPasswordForm = this.fb.group({
      newPassword: [
        "",
        [
          Validators.required,
          Validators.minLength(6),
          Validators.pattern(/^(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).{6,}$/),
        ],
      ],
      confirmPassword: ["", [Validators.required]],
    });
    this._coreConfigService.config = {
      layout: {
        navbar: {
          hidden: true,
        },
        menu: {
          hidden: true,
        },
        footer: {
          hidden: true,
        },
        customizer: false,
        enableLocalStorage: false,
      },
    };
  }

  ngOnInit() {
    this.route.paramMap.subscribe((params) => {
      this.uid = params.get("uid")!;
      this.token = params.get("token")!;
    });

    this._coreConfigService.config
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe((config) => {
        this.coreConfig = config;
      });

    this.http

      .get(
        `${environment.apiUrl}/auth/password-reset/${this.uid}/${this.token}/validate`
      )
      .subscribe(
        (response) => {
          // this.toastr.success("Link đặt lại mật khẩu hợp lệ!", "Thành công", {
          //   positionClass: "toast-top-right",
          //   toastClass: "toast ngx-toastr",
          //   closeButton: true,
          // });
        },
        (error) => {
          // // console.log(error);
          this.isExpired = true;
          // this.toastr.error(
          //   "Link đặt lại mật khẩu không hợp lệ hoặc hết hạn!",
          //   "Lỗi",
          //   {
          //     positionClass: "toast-top-right",
          //     toastClass: "toast ngx-toastr",
          //     closeButton: true,
          //   }
          // );
        }
      );
  }

  onSubmit() {
    if (this.resetPasswordForm.invalid) return;

    const { newPassword, confirmPassword } = this.resetPasswordForm.value;
    if (newPassword !== confirmPassword) {
      this.toastr.error("Mật khẩu không trùng khớp", "Lỗi", {
        positionClass: "toast-top-right",
        toastClass: "toast ngx-toastr",
        closeButton: true,
      });
      return;
    }

    this.http

      .post(
        `${environment.apiUrl}/auth/password-reset/${this.uid}/${this.token}/`,
        {
          new_password: newPassword,
          confirm_password: confirmPassword,
        }
      )
      .subscribe(
        (response) => {
          this.toastr.success(
            "Mật khẩu đã được đặt lại thành công!",
            "Thành công",
            {
              positionClass: "toast-top-right",
              toastClass: "toast ngx-toastr",
              closeButton: true,
            }
          );
          this.router.navigate(["/pages/authentication/login-v2"]);
        },
        (error) => {
          this.toastr.error(
            error.error.error || "Có lỗi xảy ra, vui lòng thử lại.",
            "Lỗi",
            {
              positionClass: "toast-top-right",
              toastClass: "toast ngx-toastr",
              closeButton: true,
            }
          );
        }
      );
  }

  /**
   * On destroy
   */
  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions
    this._unsubscribeAll.next(null);
    this._unsubscribeAll.complete();
  }
}
