import {
  AfterViewInit,
  Component,
  OnInit,
  Pipe,
  PipeTransform,
  ViewChild,
  ViewEncapsulation,
} from "@angular/core";
import { FormControl, Validators } from "@angular/forms";
import { Router } from "@angular/router";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { AuthenticationService } from "app/auth/service";
import { ChangeDataService } from "app/auth/service/change-data.service";
import { WebSocketService } from "app/auth/service/webSocket.service";
import { DocumentStatus } from "app/models/DocumentStatus";
import { FormType } from "app/models/FormType";
import { ToastrService } from "ngx-toastr";
import { interval, Subject, Subscription } from "rxjs";
import { debounceTime, finalize, takeUntil } from "rxjs/operators";
import Swal from "sweetalert2";
import { BocTachThongTinService } from "./boc-tach-thong-tin.service";

interface FileValidationError {
  file: File;
  error: string;
}

@Component({
  selector: "app-boc-tach-thong-tin",
  templateUrl: "./boc-tach-thong-tin.component.html",
  styleUrls: ["./boc-tach-thong-tin.component.scss"],
  encapsulation: ViewEncapsulation.None,
})
export class BocTachThongTinComponent implements OnInit, AfterViewInit {
  @ViewChild("editDocumentNameModal") editDocumentNameModal!: any;
  @ViewChild("viewFileModal") viewFileModal!: any;

  public hasBaseDropZoneOver: boolean = false;
  public currentUser = this._authenticationService.currentUserValue;
  public listFileOCR: any;
  public listDocumentFilter: any;
  public totalDocument: number;
  public page = 1;
  public currentPage: number = 1;
  public type: FormType;
  public _unSubAll: Subject<any> = new Subject();
  public validationErrors: FileValidationError[] = [];
  public documentStatus = DocumentStatus;
  public fileExtension: any;
  // Set maximum file size (5MB)se
  readonly MAX_FILE_SIZE = JSON.parse(localStorage.getItem("package"))
    .max_file_size_per_upload;
  readonly MAX_FILES = JSON.parse(localStorage.getItem("package"))
    .max_num_doc_per_upload;
  selectedDocument: any = {};
  public fileName: FormControl = new FormControl("", [
    Validators.required,
    Validators.maxLength(255),
  ]);
  public workSpaceId: string = "";
  public total: number = 0;
  public searchDocument: FormControl = new FormControl("");
  private autoFetchSub?: Subscription;

  private startAutoFetch() {
    this.autoFetchSub?.unsubscribe();
    let second = 0;
    this.autoFetchSub = interval(1000).subscribe(() => {
      second++;
      if (second % 7 === 0) {
        this.getAllFileOCR(this.searchDocument.value, null);
      }
    });
  }
  itemDeleting: any[] = [];
  isUploading: boolean = false;
  constructor(
    private _bocTachService: BocTachThongTinService,
    private _authenticationService: AuthenticationService,
    private changeData: ChangeDataService,
    private _toastrService: ToastrService,
    private modalService: NgbModal,
    private webSocketService: WebSocketService,
    private router: Router
  ) {
    this.startAutoFetch();
  }
  viewFile(file) {
    this.router.navigate([], {
      queryParams: {
        fileId: file.id,
        tabs: "toanvan",
        luocdo: null, // xoá đi khi xem tài liệu khác, tránh gọi đến lược đồ es
        type: "upload",
        time: new Date().getTime(),
        fileName: file.name,
        save: false,
      },
      queryParamsHandling: "merge", // Giữ lại các query params khác nếu có
    });
    this.modalOpen(this.viewFileModal, FormType.Convert, "xl");
  }

  ngOnInit(): void {
    this.webSocketService.messageSubject
      .pipe(takeUntil(this._unSubAll))
      .subscribe((res) => {
        const idFile = res.data.data.document_id || res.data.data.id;
        // // console.log("id", idFile);

        const item = this.listFileOCR.find((file) => file.id === idFile);

        if (item) {
          item.status_display = res.event;
        }
        if (
          res.data.data.status == DocumentStatus.STATUS_SUCCESS &&
          res.data.event == "CDVB"
        ) {
          this.getAllFileOCR("", null);
        }
      });
    this.searchDocument.valueChanges
      .pipe(takeUntil(this._unSubAll), debounceTime(500))
      .subscribe((res) => {
        this.page = 1;
        if (res) {
          this.getAllFileOCR(res, null);
        } else {
          this.getAllFileOCR("", null);
        }
      });
    this.workSpaceId = localStorage.getItem("workspace_id") || "";
    this.getAllFileOCR("", null);
  }

  ngAfterViewInit() { }

  download(item) {
    const fileType = item.name.includes(".")
      ? item.name.split(".").pop().toLowerCase()
      : "pdf";

    this._bocTachService.saveFile(item, fileType).subscribe({
      next: () => { },
      error: () => {
        this._toastrService.error("File gốc chưa có trên cơ sở dữ liệu!", "Lỗi", {
          closeButton: true,
          positionClass: "toast-top-right",
          toastClass: "toast ngx-toastr",
        });
      }
    });
  }

  downloadFileSearch(item) {
    const fileType = item.name.includes(".")
      ? item.name.split(".").pop().toLowerCase()
      : "pdf";

    this._bocTachService.saveFileSearch(item, fileType).subscribe({
      next: () => { },
      error: () => {
        this._toastrService.error("File gốc chưa có trên cơ sở dữ liệu!", "Lỗi", {
          closeButton: true,
          positionClass: "toast-top-right",
          toastClass: "toast ngx-toastr",
        });
      }
    });
  }

  downloadFileConvert(item, type) {
    this._bocTachService.saveFile(item, type).subscribe({
      next: () => { },
      error: () => {
        this._toastrService.error("File chuyển đổi chưa có trên cơ sở dữ liệu!", "Lỗi", {
          closeButton: true,
          positionClass: "toast-top-right",
          toastClass: "toast ngx-toastr",
        });
      }
    });
  }


  reLoadFile(item) {
    this._bocTachService.reloadFile(item.id).subscribe((res) => {
      setTimeout(() => {
        this.getAllFileOCR("", null);
      }, 1000);
    });
  }

  deleteDocument(item) {
    Swal.fire({
      title: "Bạn có chắc chắn muốn xóa?",
      icon: "warning",
      reverseButtons: true,
      showCancelButton: true,
      confirmButtonColor: "#008fd3",
      cancelButtonColor: "#EEE",
      customClass: {
        confirmButton: "swal-confirm",
        cancelButton: "swal-cancel",
      },
      confirmButtonText: "Xóa",
      cancelButtonText: "Hủy",
    }).then((result) => {
      if (result.isConfirmed) {
        this.itemDeleting.push(item.id);
        this._bocTachService.deleteDocument(item.id)
          .pipe(finalize(() => { this.itemDeleting = this.itemDeleting.filter(id => id !== item.id); }))
          .subscribe({
            next: (res) => {
              this._toastrService.success(
                "Tài liệu đã được xóa thành công!",
                "Thành công",
                {
                  closeButton: true,
                  positionClass: "toast-top-right",
                  toastClass: "toast ngx-toastr",
                }
              );
              setTimeout(() => {
                this.getAllFileOCR("", null);
              }, 1000);
            },
            error: (err) => {
              this._toastrService.error(
                "Xóa tài liệu thất bại. Vui lòng thử lại!",
                "Lỗi",
                {
                  closeButton: true,
                  positionClass: "toast-top-right",
                  toastClass: "toast ngx-toastr",
                }
              );
            },
          });
      }
    });
  }

  getAllFileOCR(search, sort_field) {
    const params: any = {
      page: this.page,
      workspace_id: this.workSpaceId,
      search: search,
    };

    if (sort_field) {
      params.sort_field = sort_field;
    }

    this._bocTachService.getAllDocumentInWorkSpace(params).subscribe((res) => {
      this.listFileOCR = res.results;
      this.listDocumentFilter = res.results;
      this.total = res.count;
    });
  }

  onPageChange(event) {
    this.currentPage = event;
    this.getAllFileOCR(this.searchDocument.value, null);
  }
  addFile(event) {
    this.isUploading = true;
    const input = event.target as HTMLInputElement;
    if (!input.files) return;

    const files: FileList = input.files;
    const allowedExtensions = ["pdf"];
    const maxFileSizeMB = JSON.parse(
      localStorage.getItem("package")
    ).max_file_size_per_upload; // Giới hạn dung lượng file

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const fileSizeMB = file.size / (1024 * 1024); // Đổi đơn vị MB
      const fileExtension = file.name.split(".").pop().toLowerCase();

      if (fileSizeMB > maxFileSizeMB / 1000) {
        this._toastrService.error(
          `Tài liệu ${file.name} quá lớn (> ${maxFileSizeMB / 1000}MB) ❌`,
          "Lỗi",
          {
            toastClass: "toast ngx-toastr",
            positionClass: "toast-top-right",
            closeButton: true,
          }
        );
        continue; // Bỏ qua file này, tiếp tục với file khác
      }

      if (!allowedExtensions.includes(fileExtension)) {
        this._toastrService.error(
          `Tài liệu ${file.name} không đúng định dạng PDF`,
          "Sai định dạng 🚫",
          {
            toastClass: "toast ngx-toastr",
            positionClass: "toast-top-right",
            closeButton: true,
          }
        );
        continue; // Bỏ qua file này, tiếp tục với file khác
      }

      // Nếu hợp lệ, thêm vào formData và gửi lên server
      const formData = new FormData();
      formData.append("origin", file);
      formData.append("workspace_id", this.workSpaceId);

      this._bocTachService.addFile(formData)
        .pipe(finalize(() => { this.isUploading = false; }))
        .subscribe(() => {
          formData.delete("origin");
          setTimeout(() => {
            this.getAllFileOCR("", null);
          }, 1000);
        });
    }

    // Reset giá trị input file để có thể chọn lại file trùng nhau
    event.target.value = "";
  }

  updateName(modal: any) {
    const newFileName = this.fileName.value + this.fileExtension;
    if (!this.fileName.value.trim()) {
      this._toastrService.warning(
        "Tên tài liệu không được để trống!",
        "Cảnh báo",
        {
          closeButton: true,
          positionClass: "toast-top-right",
          toastClass: "toast ngx-toastr",
        }
      );
      return;
    }
    if (this.fileName.value.trim().length > 255) {
      this._toastrService.warning("Không được quá 255 kí tự!", "Cảnh báo", {
        closeButton: true,
        positionClass: "toast-top-right",
        toastClass: "toast ngx-toastr",
      });
      return;
    }

    this._bocTachService
      .updateDocumentName(this.selectedDocument.id, newFileName)
      .subscribe({
        next: () => {
          this._toastrService.success(
            "Cập nhật tên tài liệu thành công!",
            "Thành công",
            {
              closeButton: true,
              positionClass: "toast-top-right",
              toastClass: "toast ngx-toastr",
            }
          );
          this.getAllFileOCR("", null);
          modal.close();
        },
        error: () => {
          this._toastrService.error("Có lỗi xảy ra khi cập nhật!", "Lỗi", {
            closeButton: true,
            positionClass: "toast-top-right",
            toastClass: "toast ngx-toastr",
          });
        },
      });
  }
  openEditModal(document: any) {
    this.selectedDocument = { ...document };
    // Lấy tên file và tách phần mở rộng
    const fileName = this.selectedDocument.name;
    const lastDotIndex = fileName.lastIndexOf(".");

    if (lastDotIndex !== -1) {
      this.fileExtension = fileName.substring(lastDotIndex); // Lấy phần mở rộng
      // this.selectedDocument.file.name = fileName.substring(0, lastDotIndex); // Lấy phần tên gốc
      this.fileName.patchValue(fileName.substring(0, lastDotIndex));
    } else {
      this.fileExtension = ""; // Trường hợp không có dấu chấm
      // this.selectedDocument.file.name = fileName;
      this.fileName.patchValue(fileName);
    }

    this.modalService.open(this.editDocumentNameModal, {
      centered: true,
      size: "lg",
    });
  }
  closeModal() {
    this.modalService.dismissAll();
  }

  modalOpen(modalSM, type: FormType, size) {
    this.modalService.open(modalSM, {
      centered: true,
      size: size,
    });
    this.type = type;
  }
  ngOnDestroy() {
    this._unSubAll.next(null);
    this._unSubAll.complete();
    this.changeData.changeData.next(false);
    this.autoFetchSub?.unsubscribe();
  }
}

@Pipe({
  name: "errorMessage",
})
export class ErrorMessagePipe implements PipeTransform {
  transform(value: string, ...args: any[]) {
    if (value) return JSON.parse(value).message;
  }
}
