<div
  class="card-body p-1 file-container ws-docs-list"
  ng2FileDrop
  [uploader]="uploader"
  (onFileDrop)="onFileDrop($event)"
>
  <div class="header-document d-flex justify-content-between">
    <h3 class="font-weight-bolder font-medium-2">Danh sách tài liệu</h3>
    <div>
      <!-- <button 
        ngbTooltip="Soạn thảo với AI"
        container="body"
        (click)="openChatbot()"
        type="button"
        class="btn btn-icon rounded-circle btn-outline-secondary mr-1"
        aria-label="Soạn thảo với AI"
        rippleEffect
      >
        <span [data-feather]="'edit-3'"></span>
      </button> -->
      <button
        class="btn btn-icon rounded-circle btn-spn-uploading btn-outline-secondary mr-1"
        rippleEffect
        disabled
        ngbTooltip="Đang tải lên..."
        container="body"
        *ngIf="isUploading"
      >
        <span class="spinner-border spinner-border-sm" role="status">
          <span class="sr-only">Loading...</span>
        </span>
      </button>
      <button
        ngbTooltip="Thêm tài liệu"
        container="body"
        (click)="fileInput.click(); fileInput.value = ''"
        type="button"
        class="btn btn-icon rounded-circle btn-outline-secondary mr-1"
        rippleEffect
        *ngIf="!isUploading"
      >
        <span [data-feather]="'upload'"></span>
      </button>
      <input
        type="file"
        class="d-none"
        #fileInput
        (change)="addDocumentInWorkSpace($event); fileInput.value = ''"
        accept=".pdf , .docx, .doc"
        multiple
      />
      <button
        [disabled]="isHaveFileIdInUrl"
        [ngbTooltip]="isHaveFileIdInUrl ? 'Vui lòng chọn tài liệu' : 'Tìm kiếm'"
        container="body"
        (click)="toggleSidebar()"
        type="button"
        class="btn btn-icon rounded-circle"
        [ngClass]="{
          'btn-outline-secondary': !isSidebarExpanded,
          'btn-primary-theme': isSidebarExpanded
        }"
        rippleEffect
      >
        <span [data-feather]="'search'"></span>
      </button>
    </div>
  </div>
  <div class="search-work-space">
    <div class="d-flex p-0 mt-1">
      <div class="col-xl-6 p-0">
        <div class="form-group">
          <input
            type="text"
            class="form-control"
            id="basicInput"
            placeholder="Tìm kiếm tài liệu"
            [formControl]="searchDocument"
          />
        </div>
      </div>
      <div class="col-5 ml-auto px-0 row">
        <ng-select
          id="file1"
          class="w-100 col-8 p-0"
          [items]="sortFile"
          placeholder="Sắp xếp"
          [clearable]="false"
          (change)="sortDocumet($event)"
        ></ng-select>
        <span
          class="col-4 cursor-pointer pl-0"
          (click)="loaiSapXep()"
          [ngbTooltip]="
            typeSort == 'arrow-down-circle' ? 'Giảm dần' : 'Tăng dần'
          "
          container="body"
        >
          <img
            src="assets/images/icons/desc.svg"
            alt="desc"
            *ngIf="typeSort == 'arrow-down-circle'"
          />
          <img
            src="assets/images/icons/asc.svg"
            alt="desc"
            *ngIf="typeSort != 'arrow-down-circle'"
          />
        </span>
      </div>
    </div>
  </div>
  <!-- dragula="multiple-list-group" -->

  <ng-container *ngIf="isFetchingDocuments">
    <div
      class="loading-document-list d-flex justify-content-center align-items-center height-100vh"
    >
      <span class="spinner-border text-primary position-absolute" role="status">
        <span class="sr-only">Loading...</span>
      </span>
    </div>
  </ng-container>
  <ng-container *ngIf="!isFetchingDocuments">
    <div
      class="list-document collapse-icon"
      id="work-space"
      #scrollMe
      [scrollTop]="scrollTop"
      (scroll)="onScroll($event)"
    >
      <!-- file đang tìm kiếm -->
      <ng-container *ngFor="let item of fileTermFormSearch; let i = index">
        <div [ngClass]="selectedFile?.id == item.id ? 'showFile' : ''">
          <div class="collapse-icon">
            <ngb-accordion
              activeIds="panel1"
              #acc="ngbAccordion"
              (panelChange)="onPanelChangeTerm($event, item)"
            >
              <ngb-panel id="panel1">
                <ng-template ngbPanelTitle>
                  <div class="document-card">
                    <img
                      src="assets/images/icons/file-search.svg"
                      alt="PDF"
                      class="icon"
                    />
                    <div class="document-info">
                      <div class="document-title-container">
                        <div class="">
                          <p class="m-0 font-weight-bolder text-primary">
                            Đang tìm kiếm
                          </p>
                          <p
                            class="font-weight-bolder one-line mr-2"
                            [ngClass]="
                              item.types == typeDocument.UPLOAD ? 'm-0' : 'mt-2'
                            "
                            [ngbTooltip]="
                              (item?.name
                                ? item.name
                                : item?.document_data?.title
                              ) | stripHtml
                            "
                            container="body"
                            placement="right"
                          >
                            {{
                              (item?.name ? item.name : item?.title) | stripHtml
                            }}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </ng-template>
                <ng-template ngbPanelContent>
                  <div
                    class="review-header flex-sm-wrap flex-xxl-wrap fs-13-custom"
                  >
                    <div class="review-title fs-13-custom">
                      Danh sách ({{ listDieuKhoanSearch.length }})
                    </div>
                  </div>

                  <div itemSize="50" class="review-list">
                    <div
                      *ngFor="let clause of listDieuKhoanSearch; let j = index"
                      class="review-item-2 cursor-pointer fs-13-custom"
                      [ngClass]="{
                        'item-selected':
                          selectedClause &&
                          (clause.id ?? clause.clause_id ?? clause.term_id) ===
                          (selectedClause.id ?? selectedClause.clause_id ?? selectedClause.term_id),
                        'item-error':
                          clause.status === DocumentStatus.STATUS_FAILED,
                        'item-success':
                          clause.status === DocumentStatus.STATUS_SUCCESS
                      }"
                      (click)="scrollToClauseInDocument(clause)"
                    >
                      <div class="item-title-2">
                        {{ clause.position }}
                        {{ clause.title }}
                      </div>
                    </div>
                  </div>
                </ng-template>
              </ngb-panel>
            </ngb-accordion>
          </div>
        </div>
      </ng-container>
      <!-- file đang tìm kiếm -->

      <!-- file lưu trong db -->
      <!-- Wrapper chứa toàn bộ danh sách có dragula -->

      <div
        dragula="handle-list"
        [dragulaModel]="listDocumentFilter"
        #containerRef
        class="draggable list-group"
      >
        <ng-container *ngFor="let item of listDocumentFilter; let i = index">
          <div
            class="document-card-wrapper"
            (contextmenu)="onRightClickFile($event, item)"
            [ngClass]="{
              showFile: selectedFile?.id == item.id,
              'border-red': item.status == DocumentStatus.STATUS_FAILED,
              'border-yellow':
                item.status != DocumentStatus.STATUS_SUCCESS &&
                item.status != DocumentStatus.STATUS_FAILED
            }"
          >
            <ng-container
              *ngIf="
                item.status == DocumentStatus.STATUS_SUCCESS;
                else otherStatus
              "
            >
              <div class="collapse-icon">
                <ngb-accordion
                  #acc="ngbAccordion"
                  [(activeIds)]="activeIds"
                  (panelChange)="onPanelChange($event, item)"
                >
                  <ngb-panel [id]="'doc-' + item.id">
                    <ng-template ngbPanelTitle>
                      <div
                        class="document-card"
                        (mouseenter)="hoveredItem = item"
                        (mouseleave)="hoveredItem = null"
                      >
                        <img
                          [src]="
                            hoveredItem === item
                              ? 'assets/images/icons/move.svg'
                              : getOfficeIcon(item)
                          "
                          alt="icon"
                          class="icon handle"
                          [ngClass]="
                            hoveredItem === item
                              ? 'cursor-move'
                              : 'cursor-pointer'
                          "
                        />
                        <div class="document-info">
                          <div class="document-title-container">
                            <div class="document-title one-line w-100">
                              <p
                                class="font-weight-bolder truncate one-line fs-6 wid-280"
                                [ngClass]="{
                                  'm-0 mt-4px':
                                    item.types == typeDocument.UPLOAD
                                }"
                                [ngbTooltip]="item.name"
                                container="body"
                                placement="right"
                              >
                                {{ i + (page - 1) * 12 + 1 }}. {{ item.name }}
                              </p>
                            </div>
                          </div>
                          <div
                            class="document-meta"
                            *ngIf="item.types == typeDocument.CSDL && item.es_id"
                          >
                            Hiệu lực:
                            <b
                              [ngClass]="{
                                'text-danger':
                                  item.tinh_trang_hieu_luc === 'Hết hiệu lực toàn bộ',
                                'text-success':
                                  item.tinh_trang_hieu_luc === 'Còn hiệu lực',
                                'text-warning':
                                  item.tinh_trang_hieu_luc === 'Hết hiệu lực một phần',
                                'text-primary':
                                  item.tinh_trang_hieu_luc === 'Chưa có hiệu lực',
                                'text-secondary':
                                  item.tinh_trang_hieu_luc === 'Ngưng hiệu lực một phần',
                                'text-muted':
                                  item.tinh_trang_hieu_luc === 'Không còn phù hợp'
                              }"
                            >
                              {{ item.tinh_trang_hieu_luc || "..." }}
                            </b>
                          </div>
                        </div>
                      </div>
                    </ng-template>

                    <ng-template ngbPanelContent>
                      <div
                        class="review-header flex-sm-wrap flex-xxl-wrap fs-13-custom"
                      >
                        <div
                          class="review-title fs-13-custom flex items-center justify-between"
                        >
                          <span class="mr-3px">
                            Danh sách
                            <span *ngIf="!isFetchingClauseOfFile"
                              >({{ listDieuKhoan.length }})</span
                            >
                          </span>
                          <button
                            [ngbTooltip]="'Lọc chính xác theo từ khóa'"
                            container="body"
                            (click)="toggleSearch()"
                            type="button"
                            class="btn btn-icon btn-sm rounded-circle"
                            [ngClass]="{
                              'btn-outline-secondary': !showSearch,
                              'btn-primary': showSearch
                            }"
                            rippleEffect
                          >
                            <span [data-feather]="'search'"></span>
                          </button>
                        </div>

                        <div
                          *ngIf="
                            item.types == typeDocument.UPLOAD ||
                            item.types == typeDocument.CSDL
                          "
                          class="review-actions d-flex align-items-center gap-2"
                        >
                          <p
                            class="text-primary cursor-pointer m-0"
                            (click)="showModalRaSoatCuThe()"
                            *ngIf="listDieuKhoan.length"
                            [ngbTooltip]="
                              countCoundition !== 0
                                ? 'Điều kiện rà soát: ' +
                                  listConditionRaSoat.so_hieu.join(', ')
                                : 'Thêm điều kiện để thực hiện rà soát'
                            "
                          >
                            {{
                              countCoundition === 0
                                ? "Điều kiện"
                                : "Điều kiện " + "( " + countCoundition + " )"
                            }}
                          </p>

                          <p
                            class="text-primary cursor-pointer m-0"
                            (click)="raSoatAll()"
                            *ngIf="listDieuKhoan.length"
                          >
                            Rà soát
                          </p>

                          <!-- <p
                            *ngIf="item.types == typeDocument.CSDL"
                            class="text-primary cursor-pointer m-0"
                            (click)="addDieuKhoan(item)"
                          >
                            Thêm
                          </p> -->

                          <button
                            [ngbTooltip]="'Thêm'"
                            container="body"
                            (click)="addDieuKhoan(item)"
                            type="button"
                            class="btn btn-icon btn-sm rounded-circle btn-outline-secondary"
                            rippleEffect
                          >
                            <span [data-feather]="'plus'"></span>
                          </button>
                        </div>
                      </div>
                      <div *ngIf="showSearch" class="mt-1 mb-1">
                        <input
                          type="text"
                          placeholder="Lọc chính xác theo từ khóa"
                          class="form-control w-full"
                          [(ngModel)]="searchText"
                          (ngModelChange)="onSearchChange()"
                        />
                      </div>
                      <div itemSize="50" class="review-list">
                        <div
                          class="loading-detail-clause-content d-flex justify-content-center align-items-center mt-1 mb-1"
                          *ngIf="isFetchingClauseOfFile"
                        >
                          <span
                            class="spinner-border spinner-border-sm text-primary position-absolute"
                            role="status"
                          >
                            <span class="sr-only">Loading...</span>
                          </span>
                        </div>
                        <div
                          *ngIf="
                            listDieuKhoan.length > 0 && !isFetchingClauseOfFile
                          "
                        >
                          <div
                            *ngFor="let clause of listDieuKhoan; let j = index"
                            (contextmenu)="
                              onRightClickClause($event, clause);
                              $event.stopPropagation()
                            "
                            class="review-item cursor-pointer fs-13-custom"
                            [ngClass]="{
                              'item-selected':
                                selectedClause &&
                                (clause.id ?? clause.clause_id ?? clause.term_id) ===
                                (selectedClause.id ?? selectedClause.clause_id ?? selectedClause.term_id),
                              'item-error':
                                clause.status === DocumentStatus.STATUS_FAILED,
                              'item-success':
                                clause.status === DocumentStatus.STATUS_SUCCESS
                            }"

                            (click)="scrollToClauseInDocument(clause)"
                          >
                            <div class="item-title">
                              {{ i + (page - 1) * 12 + 1 }}.{{ j + 1 }}
                              {{ clause.position }} {{ clause.title }}
                            </div>

                            <div
                              *ngIf="
                                item.types == typeDocument.UPLOAD ||
                                item.types == typeDocument.CSDL
                              "
                              class="item-status"
                            >
                              <ng-container [ngSwitch]="clause.status">
                                <p
                                  *ngSwitchCase="DocumentStatus.STATUS_FAILED"
                                  class="m-0 text-danger"
                                  [ngClass]="{
                                    'item-selected':
                                      clause.id == selectedClause?.id
                                  }"
                                  (click)="
                                    raSoatDieuKhoan(clause);
                                    $event.stopPropagation()
                                  "
                                >
                                  Rà soát
                                  <span data-feather="alert-circle"></span>
                                </p>

                                <p
                                  *ngSwitchCase="DocumentStatus.STATUS_SUCCESS"
                                  class="m-0 text-success"
                                  [ngClass]="{
                                    'item-selected':
                                      clause.id == selectedClause?.id
                                  }"
                                  (click)="
                                    selectClause(clause);
                                    $event.stopPropagation()
                                  "
                                >
                                  Rà soát
                                  <span data-feather="check-circle"></span>
                                </p>

                                <p
                                  *ngSwitchCase="
                                    DocumentStatus.STATUS_PROCESSING
                                  "
                                  class="m-0 text-primary"
                                  [ngClass]="{
                                    'item-selected':
                                      clause.id == selectedClause?.id
                                  }"
                                >
                                  Rà soát
                                  <span
                                    class="spinner-border spinner-border-sm text-primary"
                                    role="status"
                                  >
                                    <span class="sr-only">Loading...</span>
                                  </span>
                                </p>

                                <p
                                  *ngSwitchCase="DocumentStatus.STATUS_PENDING"
                                  class="m-0 text-primary"
                                  [ngClass]="{
                                    'item-selected':
                                      clause.id == selectedClause?.id
                                  }"
                                >
                                  Chờ xử lý
                                </p>

                                <p
                                  *ngSwitchDefault
                                  class="m-0 text-muted"
                                  [ngClass]="{
                                    'item-selected':
                                      clause.id == selectedClause?.id
                                  }"
                                  (click)="
                                    raSoatDieuKhoan(clause);
                                    $event.stopPropagation()
                                  "
                                >
                                  Rà soát
                                </p>
                              </ng-container>
                            </div>
                          </div>
                        </div>
                        <div
                          *ngIf="
                            listDieuKhoan.length == 0 && !isFetchingClauseOfFile
                          "
                          class="text-center text-muted p-1 fs-13-custom"
                        >
                          Không có điều khoản
                        </div>
                      </div>
                    </ng-template>
                  </ngb-panel>
                </ngb-accordion>
              </div>
            </ng-container>

            <ng-template #otherStatus>
              <div
                class="document-card cursor-pointer"
                (mouseenter)="hoveredItem = item"
                (mouseleave)="hoveredItem = null"
                [ngClass]="{ showFile: selectedFile?.id == item.id }"
              >
                <img
                  [src]="
                    hoveredItem === item
                      ? 'assets/images/icons/move.svg'
                      : getOfficeIcon(item)
                  "
                  alt="PDF"
                  class="icon handle"
                  [ngClass]="
                    hoveredItem === item ? 'cursor-move' : 'cursor-pointer'
                  "
                />
                <div class="cursor-pointer mt-2px w-100">
                  <div class="">
                    <div
                      [ngClass]="{
                        'm-0': item.types == typeDocument.UPLOAD,
                        'my-1': item.types != typeDocument.UPLOAD,
                        'one-line wid-260':
                          item.status == DocumentStatus.STATUS_FAILED ||
                          item.status == DocumentStatus.STATUS_PROCESSING ||
                          item.status == DocumentStatus.STATUS_CONFLICT ||
                          item.status == DocumentStatus.STATUS_OCR ||
                          item.status == DocumentStatus.STATUS_IE,
                        'one-line wid-270':
                          item.status == DocumentStatus.STATUS_PENDING
                      }"
                    >
                      {{ i + (page - 1) * 12 + 1 }}.{{ item.name }}
                    </div>
                  </div>
                </div>

                <ng-container [ngSwitch]="item.status">
                  <div
                    *ngSwitchCase="DocumentStatus.STATUS_PENDING"
                    class="action-file-2 text-primary ms-auto d-flex justify-content-end ml-auto"
                  >
                    Chờ xử lý
                  </div>

                  <div
                    *ngSwitchCase="DocumentStatus.STATUS_PROCESSING"
                    class="action-file text-primary d-flex justify-content-end pr-1 cursor-pointer"
                  >
                    <div
                      [ngbTooltip]="item.status_display"
                      placement="right"
                      container="body"
                      class="spinner-border spinner-border-sm text-primary"
                      role="status"
                    >
                      <span class="sr-only">Loading...</span>
                    </div>
                  </div>
                  <div
                    *ngSwitchCase="DocumentStatus.STATUS_PROCESSING"
                    class="action-file text-primary d-flex justify-content-end pr-1 cursor-pointer"
                  >
                    <div
                      [ngbTooltip]="item.status_display"
                      placement="right"
                      container="body"
                      class="spinner-border spinner-border-sm text-primary"
                      role="status"
                    >
                      <span class="sr-only">Loading...</span>
                    </div>
                  </div>
                  <div
                    *ngSwitchCase="DocumentStatus.STATUS_CONFLICT"
                    class="action-file text-primary d-flex justify-content-end pr-1 cursor-pointer"
                  >
                    <div
                      [ngbTooltip]="item.status_display"
                      placement="right"
                      container="body"
                      class="spinner-border spinner-border-sm text-primary"
                      role="status"
                    >
                      <span class="sr-only">Loading...</span>
                    </div>
                  </div>
                  <div
                    *ngSwitchCase="DocumentStatus.STATUS_OCR"
                    class="action-file text-primary d-flex justify-content-end pr-1 cursor-pointer"
                  >
                    <div
                      [ngbTooltip]="item.status_display"
                      placement="right"
                      container="body"
                      class="spinner-border spinner-border-sm text-primary"
                      role="status"
                    >
                      <span class="sr-only">Loading...</span>
                    </div>
                  </div>
                  <div
                    *ngSwitchCase="DocumentStatus.STATUS_IE"
                    class="action-file text-primary d-flex justify-content-end ml-auto pr-1 cursor-pointer"
                  >
                    <div
                      [ngbTooltip]="item.status_display"
                      placement="right"
                      container="body"
                      class="spinner-border spinner-border-sm text-primary"
                      role="status"
                    >
                      <span class="sr-only">Loading...</span>
                    </div>
                  </div>

                  <div
                    *ngSwitchDefault
                    class="action-file text-primary ml-auto text-right"
                    (click)="rerunFile(item); $event.stopPropagation()"
                  >
                    Xử lý
                  </div>
                </ng-container>
              </div>
            </ng-template>
          </div>
        </ng-container>
      </div>
      <!-- file lưu trong db -->

      <!-- context-menu -->
      <div
        class="context-menu"
        *ngIf="contextMenuVisible"
        [style.top.px]="contextMenuPosition.y"
        [style.left.px]="contextMenuPosition.x"
        (click)="closeContextMenu()"
      >
        <ul>
          <li (click)="renameDocument(contextMenuItem)">
            <span data-feather="edit-2" class="mr-25"></span> Đổi tên
          </li>
          <li
            *ngIf="contextMenuItem.types == typeDocument.CSDL"
            (click)="downloadFileFromSearch(contextMenuItem, 'pdf')"
          >
            <span data-feather="download" class="mr-25"></span> Tải xuống
          </li>
          <li
            *ngIf="
              contextMenuItem.types == typeDocument.UPLOAD &&
              !contextMenuItem.name.includes('.docx') &&
              !contextMenuItem.name.includes('.doc')
            "
            (click)="downloadFile(contextMenuItem, 'pdf')"
          >
            <span data-feather="download" class="mr-25"></span> Tải xuống PDF
          </li>
          <li
            *ngIf="contextMenuItem.types == typeDocument.UPLOAD"
            (click)="downloadFile(contextMenuItem, 'docx')"
          >
            <span data-feather="download" class="mr-25"></span> Tải xuống DOCX
          </li>
          <li (click)="getFileReport(contextMenuItem.id, false)">
            <span data-feather="download" class="mr-25"></span> Xuất điều đã rà
            soát
          </li>
          <li (click)="getReportAllConflict(contextMenuItem.id, 'mau_thuan')">
            <span data-feather="download" class="mr-25"></span> Xuất toàn bộ mâu thuẫn
          </li>
          <li (click)="getFileReport(contextMenuItem.id)">
            <span data-feather="download" class="mr-25"></span> Xuất toàn bộ tài
            liệu
          </li>

          <li (click)="deleteDocument(contextMenuItem)">
            <span data-feather="trash-2" class="mr-25"></span> Xóa
          </li>
        </ul>
      </div>
      <div
        class="context-menu"
        *ngIf="contextMenuClause"
        [style.top.px]="contextMenuPosition.y"
        [style.left.px]="contextMenuPosition.x"
        (click)="closeContextMenu()"
      >
        <ul>
          <li (click)="raSoatDieuKhoan(contextClauseItem)">
            <span data-feather="refresh-cw" class="mr-25"></span> Rà soát lại
          </li>
          <li (click)="updateClause(contextClauseItem)">
            <span data-feather="edit-2" class="mr-25"></span> Cập nhật điều
            khoản
          </li>

          <li (click)="deleteClasue(contextClauseItem)">
            <span data-feather="trash-2" class="mr-25"></span> Xoá
          </li>
        </ul>
      </div>
      <!-- context-menu -->

      <!-- no-data -->
      <ng-container
        *ngIf="
          listDocument?.length == 0 &&
          fileTermFormSearch?.length == 0 &&
          !isFetchingDocuments
        "
      >
        <div
          class="d-flex flex-column justify-content-center align-items-center height-70vh"
        >
          <img src="assets/images/icons/no-file.svg" alt="no-file" />
          <p class="font-weight-bolder h3 mt-2">
            Vui lòng thêm tài liệu để bắt đầu
          </p>
        </div>
      </ng-container>
      <!-- no-data -->
    </div>
  </ng-container>

  <!-- paginate-->
  <div
    class="d-flex w-100 justify-content-center paginate-document"
    *ngIf="total > 12"
  >
    <ngb-pagination
      [maxSize]="3"
      [collectionSize]="total"
      [(page)]="page"
      pageSize="12"
      aria-label="Default pagination"
      [rotate]="true"
      [ellipses]="false"
      [boundaryLinks]="true"
      (pageChange)="onPageChange($event)"
    >
    </ngb-pagination>
  </div>
  <!-- paginate-->
</div>
<ng-template #editDocumentNameModal let-modal>
  <div class="modal-body">
    <div class="col-12 p-0">
      <div class="form-group">
        <label
          for="basicTextarea"
          class="w-100 align-items-center d-flex justify-content-between"
          >Chỉnh sửa tên tài liệu
          <div class="">
            <button
              class="btn btn-sm ml-auto p-0"
              (click)="modal.dismiss('Cross click')"
            >
              <img src="assets/images/icons/x.svg" alt="x" />
            </button></div
        ></label>
      </div>
    </div>
    <div class="col-12 p-0">
      <form #editNameForm="ngForm" (ngSubmit)="updateName(modal)">
        <div class="form-group">
          <div class="d-flex align-items-center justify-content-between">
            <label for="documentName">Tên tài liệu</label>

            <div class="text-muted font-sm text-right">
              {{ fileName.value.length }}/255
            </div>
          </div>
          <!-- <label for="documentName">Tên tài liệu</label> -->
          <input
            type="text"
            id="documentName"
            class="form-control"
            [formControl]="fileName"
            name="documentName"
            required
            maxlength="255"
          />
        </div>

        <div class="w-100 justify-content-end d-flex">
          <button type="submit" class="btn btn-primary mr-1">Lưu</button>
          <button
            type="button"
            class="btn btn-secondary"
            (click)="modal.dismiss()"
          >
            Hủy
          </button>
        </div>
      </form>
    </div>
  </div>
</ng-template>
<ng-template #clauseModal let-modal>
  <app-add-clause
    [title]="title"
    [modal]="modal"
    [row]="row"
    [type]="type"
  ></app-add-clause>
</ng-template>
<ng-template #modalRaSoatCuThe let-modal>
  <div class="modal-body" tabindex="0" ngbAutofocus>
    <div class="form-group">
      <label
        for="basicTextarea"
        class="w-100 align-items-center d-flex justify-content-between"
        >Thêm danh sách điều kiện các văn bản cần được rà soát
        <div class="">
          <button
            class="btn btn-sm ml-auto p-0"
            (click)="modal.dismiss('Cross click')"
          >
            <img src="assets/images/icons/x.svg" alt="x" />
          </button></div
      ></label>
    </div>
    <div class="container-xxl p-0">
      <form (ngSubmit)="submitRaSoatCuThe()">
        <div class="row">
          <div class="col-md-12 mb-1 d-flex flex-column p-fluid">
            <label for="orgName" class="form-label text-muted">
              Văn bản căn cứ cần rà soát
            </label>

            <div class="d-flex align-items-center w-100">
              <p-chips
                #chipsRef
                id="orgName"
                class="flex-grow-1"
                placeholder="VD: 31/2024/QH15"
                [(ngModel)]="valuesSoHieuCanRaSoat"
                [ngModelOptions]="{ standalone: true }"
                [addOnBlur]="true"
                [separator]="','"
                (onAdd)="onChipAdd(chipsGroupType.so_hieu, $event.value)"
              ></p-chips>

              <button
                type="button"
                class="btn btn-primary ml-1 text-nowrap"
                (click)="addSoHieu()"
              >
                Chọn văn bản căn cứ
              </button>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-md-12 mb-1 d-flex flex-column p-fluid">
            <label for="orgName" class="form-label text-muted"
              >Danh sách từ khoá bắt buộc có trong văn bản đích
            </label>
            <p-chips
              placeholder="Nhập danh sách từ khoá bắt buộc có trong văn bản đích"
              id="orgName"
              [(ngModel)]="valuesVanBanDich"
              [ngModelOptions]="{ standalone: true }"
              [addOnBlur]="true"
              (onAdd)="onChipAdd(chipsGroupType.van_ban_dich, $event.value)"
            ></p-chips>
          </div>
        </div>

        <div class="row on-top">
          <div class="col-md-12 mb-1 d-flex flex-column p-fluid">
            <label for="loaiVanBan" class="form-label text-muted">
              Loại văn bản đích
            </label>
            <p-multiSelect
              id="loaiVanBan"
              class="overlay-in-modal"
              [options]="loaiVanBanOptions"
              [(ngModel)]="valuesLoaiVanBan"
              [ngModelOptions]="{ standalone: true }"
              optionLabel="label"
              optionValue="value"
              display="chip"
              placeholder="Chọn loại văn bản"
              [filter]="true"
              [showClear]="true"
              [maxSelectedLabels]="3"
              appendTo="body"
              panelStyleClass="overlay-in-modal"
            ></p-multiSelect>
          </div>
        </div>
        <!-- Nơi ban hành -->
        <div class="row">
          <div class="col-md-12 mb-1 d-flex flex-column p-fluid">
            <label for="donViBanHanh" class="form-label text-muted">
              Nơi ban hành
            </label>
            <p-multiSelect
              id="donViBanHanh"
              class="overlay-in-modal"
              [options]="coQuanBanHanhOptions"
              [(ngModel)]="valuesDonViBanHanh"
              [ngModelOptions]="{ standalone: true }"
              optionLabel="label"
              optionValue="value"
              display="chip"
              placeholder="Chọn nơi ban hành"
              [filter]="true"
              [showClear]="true"
              appendTo="body"
              panelStyleClass="overlay-in-modal"
            ></p-multiSelect>
          </div>
        </div>
        <div class="row">
          <div class="col-md-12 mb-1 d-flex flex-column p-fluid">
            <label for="orgName" class="form-label text-muted"
              >Tình trạng hiệu lực
            </label>
            <p-multiSelect
              id="loaiVanBan"
              class="overlay-in-modal"
              multiple="true"
              [options]="listTrangThaiHieuLuc"
              [(ngModel)]="valuesTinhTrangHieuLuc"
              [ngModelOptions]="{ standalone: true }"
              optionLabel="label"
              optionValue="value"
              display="chip"
              placeholder="Chọn tình trạng hiệu lực"
              [filter]="true"
              [showClear]="true"
              appendTo="body"
              panelStyleClass="overlay-in-modal"
            ></p-multiSelect>
          </div>
        </div>
        <!-- Ngày ban hành (daterange) -->
        <div class="row">
          <div class="col-md-12 mb-1 d-flex flex-column p-fluid">
            <label class="form-label text-muted">Ngày ban hành</label>
            <ng2-flatpickr
              #pickerNBH
              class="daterange-control"
              [config]="dateRangeNBHConfig"
              [(ngModel)]="modelNgayBanHanh"
              [ngModelOptions]="{ standalone: true }"
              (ngModelChange)="onNgayBanHanhModel($event)"
              (onClose)="onPickNgayBanHanh($event)"
            >
              <!-- <input type="text" class="form-control daterange-input" data-input placeholder="Tất cả" /> -->
              <!-- <button
                type="button"
                class="btn btn-link daterange-clear"
                aria-label="Xoá ngày ban hành"
                data-clear
                (click)="clearNBH($event)"
              >
                &times;
              </button> -->
            </ng2-flatpickr>
          </div>
        </div>

        <!-- Ngày có hiệu lực (daterange) -->
        <div class="row">
          <div class="col-md-12 mb-1 d-flex flex-column p-fluid">
            <label class="form-label text-muted">Ngày có hiệu lực</label>
            <ng2-flatpickr
              #pickerNCHL
              class="daterange-control"
              [config]="dateRangeNCHLConfig"
              [(ngModel)]="modelNgayCoHieuLuc"
              [ngModelOptions]="{ standalone: true }"
              (ngModelChange)="onNgayCoHieuLucModel($event)"
              (onClose)="onPickNgayCoHieuLuc($event)"
            >
              <!-- <input type="text" class="form-control daterange-input" data-input placeholder="Tất cả" /> -->

              <!-- <button
                type="button"
                class="btn btn-link daterange-clear"
                aria-label="Xoá ngày có hiệu lực"
                data-clear
                (click)="clearNCHL($event)"
              >
                &times;
              </button> -->
            </ng2-flatpickr>
          </div>
        </div>
        <div class="w-100 justify-content-end d-flex">
          <button
            type="button"
            rippleEffect
            class="btn btn-secondary mr-1"
            (click)="modal.close('Cross click')"
          >
            Huỷ
          </button>
          <button rippleEffect type="submit" class="btn btn-primary">
            Lưu
          </button>
        </div>
      </form>
    </div>
  </div>
</ng-template>
<ng-container *ngFor="let s of (states | async) ?? []; let i = index">
  <app-progress-toast
    [state]="s"
    placement="bottom-left"
    [xOffset]="16"
    [yOffset]="16 + i * 84"
    [width]="300"
    [zIndex]="1085"
    [bs5]="true"
    (closed)="progressToastStore.remove(s.id)"
  >
  </app-progress-toast>
</ng-container>
