import { OverlayModule } from "@angular/cdk/overlay";
import { APP_BASE_HREF, DatePipe } from "@angular/common";
import { HTTP_INTERCEPTORS, HttpClientModule } from "@angular/common/http";
import { NgModule } from "@angular/core";
import { MatProgressSpinnerModule } from "@angular/material/progress-spinner";
import { BrowserModule } from "@angular/platform-browser";
import { BrowserAnimationsModule } from "@angular/platform-browser/animations";
import { PreloadAllModules, RouterModule, Routes } from "@angular/router";
import { CoreCommonModule } from "@core/common.module";
import { CoreSidebarModule, CoreThemeCustomizerModule } from "@core/components";
import { SpinnerComponent } from "@core/components/loading/spinner/spinner.component";
import { CoreModule } from "@core/core.module";
import { NgbModule } from "@ng-bootstrap/ng-bootstrap";
import { TranslateModule } from "@ngx-translate/core";
import { NgxDatatableModule } from "@swimlane/ngx-datatable";
import { coreConfig } from "app/app-config";
import { AppComponent } from "app/app.component";
import { LayoutModule } from "app/layout/layout.module";
import "hammerjs";
import { MonacoEditorModule } from "ngx-monaco-editor";
import { ToastrModule } from "ngx-toastr"; // For auth after login toast
import { JwtInterceptor } from "./auth/helpers";
import { AuthRedirectGuard } from "./auth/helpers/redirect.guards";
import { AppsModule } from "./main/apps/apps.module";
import { AuthGuard } from './auth/helpers/auth.guards';
import { LandingModule } from './main/pages/landing/landing.module';
import { LandingComponent } from './main/pages/landing/landing.component';
import { SocialAuthServiceConfig, GoogleLoginProvider } from '@abacritt/angularx-social-login';
import { environment } from "environments/environment";
import { AppLoadingInterceptor } from './shared/app-loading.interceptor';
import { GlobalLoaderComponent } from './shared/global-loader/global-loader.component';

const appRoutes: Routes = [
  // { path: '', pathMatch: 'full', component: LandingComponent },
  {
    path: 'home',
    component: LandingComponent
  },
  {
    path: 'cms',
    canActivate: [AuthGuard],
    data: { cmsRoles: ['REVIEWER','WRITER'] },
    loadChildren: () =>
      import('./main/apps/cms/cms.module').then(m => m.CmsModule)
  },
  {
    path: "pages",
    loadChildren: () =>
      import("./main/pages/pages.module").then((m) => m.PagesModule),
  },
  {
    path: "",
    component: SpinnerComponent, // phải cho 1  empty component để k bị lỗi
    canActivate: [AuthRedirectGuard],
    pathMatch: "full",
  },
  {
    path: "**",
    redirectTo: "/pages/miscellaneous/error", //Error 404 - Page not found
  },
];

@NgModule({
  declarations: [AppComponent, SpinnerComponent, GlobalLoaderComponent ],
  entryComponents: [SpinnerComponent],
  imports: [
    BrowserModule,
    BrowserAnimationsModule,
    HttpClientModule,
    RouterModule.forRoot(appRoutes, {
      scrollPositionRestoration: "enabled", // Add options right here
      relativeLinkResolution: "legacy",
      preloadingStrategy: PreloadAllModules, // preload all module in background
    }),
    TranslateModule.forRoot(),
    NgxDatatableModule.forRoot({
      messages: {
        emptyMessage: "Không có dữ liệu",
        totalMessage: "Hàng",
        selectedMessage: "Đã chọn",
      },
    }),
    //NgBootstrap
    NgbModule,
    ToastrModule.forRoot({
      toastClass: 'ngx-toastr my-toast', 
      positionClass: 'toast-top-right', 
      progressBar: true,
      closeButton: true,
      timeOut: 3500,
      extendedTimeOut: 1500,
      preventDuplicates: true,
      newestOnTop: true,
      tapToDismiss: true,
    }),
    // Core modules
    CoreModule.forRoot(coreConfig),
    CoreCommonModule,
    CoreSidebarModule,
    CoreThemeCustomizerModule,

    // App modules
    LayoutModule,
    AppsModule,
    MatProgressSpinnerModule,
    OverlayModule,
    MonacoEditorModule.forRoot(),
    LandingModule,
  ],

  bootstrap: [AppComponent],
  providers: [
    {
      provide: APP_BASE_HREF,
      useValue: "/",
    },
    { provide: HTTP_INTERCEPTORS, useClass: JwtInterceptor, multi: true },
    { provide: HTTP_INTERCEPTORS, useClass: AppLoadingInterceptor, multi: true },
    DatePipe,
    {
      provide: 'SocialAuthServiceConfig',
      useValue: {
        autoLogin: false,
        providers: [
          {
            id: GoogleLoginProvider.PROVIDER_ID,
            provider: new GoogleLoginProvider(environment.googleClientId, {
              oneTapEnabled: false
            }),
          },
        ],
      } as SocialAuthServiceConfig,
    },
  ],
})
export class AppModule {}
